/*******************************************************************************
* Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
*
* This software is owned and published by:
* CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
*
* BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
* BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
*
* This software contains source code for use with CMS
* components. This software is licensed by CMS to be adapted only
* for use in systems utilizing CMS components. CMS shall not be
* responsible for misuse or illegal use of this software for devices not
* supported herein. CMS is providing this software "AS IS" and will
* not be responsible for issues arising from incorrect user implementation
* of the software.
*
* This software may be replicated in part or whole for the licensed use,
* with the restriction that this Disclaimer and Copyright notice must be
* included with each copy of this software, whether used in part or whole,
* at all times.
*/

/****************************************************************************/
/** \file demo_gpio.c
**
**  
**
**	History:
**	
*****************************************************************************/
/****************************************************************************/
/*	include files
*****************************************************************************/
#include "demo_gpio.h"

/****************************************************************************/
/*	Local pre-processor symbols('#define')
****************************************************************************/

/****************************************************************************/
/*	Global variable definitions(declared in header file with 'extern')
****************************************************************************/

/****************************************************************************/
/*	Local type definitions('typedef')
****************************************************************************/

/****************************************************************************/
/*	Local variable  definitions('static')
****************************************************************************/

/****************************************************************************/
/*	Local function prototypes('static')
****************************************************************************/

/****************************************************************************/
/*	Function implementation - global ('extern') and local('static')
****************************************************************************/
extern bit uart;
/******************************************************************************
 ** \brief	 GPIO_Config
 ** \param [in] none
 **          GPIO interrupt config
 ** \return  none
 ** \note  
 ******************************************************************************/
void GPIO_Config(void)
{
	P0TRIS=0xff;P0=0x0;P1TRIS=0xff;P1=0x0;P2TRIS=0xff;P2=0x0;P3TRIS=0xff;P3=0x0;
  
	//A1-P14 B1-P13 A2-P04 B2-P05 (motor control pins)
	GPIO_SET_MUX_MODE(P14CFG,GPIO_MUX_GPIO);
	GPIO_SET_MUX_MODE(P13CFG,GPIO_MUX_GPIO);
	GPIO_SET_MUX_MODE(P04CFG,GPIO_MUX_GPIO);
	GPIO_SET_MUX_MODE(P05CFG,GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_4);  // P14
	GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_3);  // P13
	GPIO_ENABLE_OUTPUT(P0TRIS,GPIO_PIN_4);  // P04
	GPIO_ENABLE_OUTPUT(P0TRIS,GPIO_PIN_5);  // P05
	
	//P22-LEDG P23-LEDR
	GPIO_SET_MUX_MODE(P22CFG, GPIO_MUX_GPIO);		
	GPIO_SET_MUX_MODE(P23CFG, GPIO_MUX_GPIO);		
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_2);			
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_3);		
	P22=0;P23=0;
	
	//K1-P17 K2-P16 K3-P15 (key inputs)
	GPIO_SET_MUX_MODE(P17CFG, GPIO_MUX_GPIO);		// set as GPIO mode
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_7);			// set as input mode
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_7);				// enable pull-up
  
 	GPIO_SET_MUX_MODE(P16CFG, GPIO_MUX_GPIO);		// set as GPIO mode
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_6);			// set as input mode
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_6); 
  
	GPIO_SET_MUX_MODE(P15CFG, GPIO_MUX_GPIO);		// set as GPIO mode
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_5);			// set as input mode
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_5);
  
	//K2 falling edge interrupt
	GPIO_SET_INT_MODE(P16EICFG, GPIO_INT_FALLING);	// set as falling edge interrupt mode
	GPIO_EnableInt(GPIO1, GPIO_PIN_6_MSK);			// enable P16 interrupt
	IRQ_SET_PRIORITY(IRQ_P1, IRQ_PRIORITY_LOW);

	
	//CHG_CHARGE-P24  CHG_FULL-P25
	GPIO_SET_MUX_MODE(P24CFG, GPIO_MUX_GPIO);	
	GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_4);
	GPIO_ENABLE_UP(P2UP, GPIO_PIN_4);
	
	GPIO_SET_MUX_MODE(P25CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_5);
	GPIO_ENABLE_UP(P2UP, GPIO_PIN_5);
	
	//CHG_CHARGE P24 falling edge interrupt
	GPIO_SET_INT_MODE(P24EICFG, GPIO_INT_FALLING);	// set as falling edge interrupt mode
	GPIO_EnableInt(GPIO2, GPIO_PIN_4_MSK);			// enable interrupt
	IRQ_SET_PRIORITY(IRQ_P2, IRQ_PRIORITY_LOW);
	
	//CHG_FULL P25 falling edge interrupt
	GPIO_SET_INT_MODE(P25EICFG, GPIO_INT_FALLING);	// set as falling edge interrupt mode
	GPIO_EnableInt(GPIO2, GPIO_PIN_5_MSK);			// enable interrupt
	IRQ_SET_PRIORITY(IRQ_P2, IRQ_PRIORITY_LOW);
	
	
	IRQ_ALL_ENABLE();

}
