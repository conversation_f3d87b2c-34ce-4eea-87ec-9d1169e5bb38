/*******************************************************************************
* Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
*
* This software is owned and published by:
* CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
*
* BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
* BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
*
* This software contains source code for use with CMS
* components. This software is licensed by CMS to be adapted only
* for use in systems utilizing CMS components. CMS shall not be
* responsible for misuse or illegal use of this software for devices not
* supported herein. CMS is providing this software "AS IS" and will
* not be responsible for issues arising from incorrect user implementation
* of the software.
*
* This software may be replicated in part or whole for the licensed use,
* with the restriction that this Disclaimer and Copyright notice must be
* included with each copy of this software, whether used in part or whole,
* at all times.
*/

/****************************************************************************/
/** \file main.c
**
** 
**
**	History:
**	
*****************************************************************************/
/****************************************************************************/
/*	include files
*****************************************************************************/
#include "cms8s6990.h"
#include "define.h"
#include "demo_gpio.h"
#include "demo_uart.h"
#include <stdio.h>
#include "demo_timer.h"
#include "demo_adc.h"

#define  USE_UART 0 //是否使用UART串口通信功能，0-禁用，1-启用
#define  RF_code_addr  0 //RF接收码地址
#define  TMR1_cnts   10 //设置定时器1的初始计数值，影响定时器中断的时间间隔

uint32_t Systemclock = 24000000;   //系统时钟初始化为24MHz

bit isPoweron;
float BatV;
bit canADC;
bit ledonoff;
bit ledonoff1;  //用于运行时LED闪烁


//遥控
// uint32_t rf_dat;
// bit rev_success;
// uint16_t code1,code2,code3;
uint16_t result;
// int16_t tmr1_cnt=TMR1_cnts;
// bit no_RF_signal; //没有遥控信号标志
// uint8_t no_RF_signal_cnt;


//按键
uint8_t K1_cnt,K2_cnt,K3_cnt;	// 按键计数器，用于记录按键按下的持续时间
bit K1_cnt_EN,K2_cnt_EN,K3_cnt_EN;  // 按键计数使能标志位，用于控制是否允许对相应按键进行计数
uint8_t K1_num,K3_num;
bit keydown;  //有按键按下
volatile bit longhit;  //长按 0-短按   1-长按(超过1s)
uint16_t longhit_cnt;
//马达
extern uint16_t interval_cnt;
extern int16_t tmr4_cnt;
extern volatile int16_t tmr4_cnt1;

uint8_t direction;     //0-初始状态未知 1-正转 2-反转 
volatile bit speedup;       //换向时需要加速标志位

bit can_RF_change_status=1;
uint8_t motor_status;  //马达状态 0-停止 1-正向快  2-反向快  3-遥控正向慢(点动)  4-遥控反向慢(点动)  5-按键正向快(点动)  6-按键反向快(点动)  7-遥控正向快(点动)  8-遥控反向快(点动)
bit is_running;   //马达正在运行标志

bit batlow,batlow1;   //batlow--低电报警  batlow1--低电关机
uint8_t batlow_cnt,batlow1_cnt;

//换向加速
uint8_t dly;

bit mode;  //0-正常模式 1-测试模式(按键按一下持续旋转)

void delay1ms(unsigned int);
void delay1us(unsigned int);
// void RF_DM();
void Get_BatV();

int main(void)
{		
    // uint16_t code_tmp1,code_tmp2,code_tmp3;
	
    GPIO_Config();
    TMR0_Config();
    TMR1_Config();
    TMR4_Config();
    ADC_Config();
	
    if(USE_UART) 
    {
        UART0_Config();
    }
	
    SYS_EnableWakeUp();	//使能STOP唤醒功能
	
    //读遥控地址码
    FLASH_UnLock();
    // code1=FLASH_Read(FLASH_DATA,RF_code_addr);
    // code2=FLASH_Read(FLASH_DATA,RF_code_addr+1);
    // code3=FLASH_Read(FLASH_DATA,RF_code_addr+2);
    FLASH_Lock();

    // if(USE_UART)
    // {
    //     printf("code1=%d,code2=%d,code3=%d\r\n",code1,code2,code3);
    // }
  
    //读dly
    IRQ_ALL_DISABLE();
    FLASH_UnLock();
    dly=FLASH_Read(FLASH_DATA,0);
    FLASH_Lock();
    IRQ_ALL_ENABLE();  
    if(dly==0xff)  //当检测到dly为0xff时，为第一次使用或FLASH数据无效
    {
        dly=20;
    }
	
    while(1)
    {
        while((K1==1)&&(K2==1)&&(K3==1)&&(isPoweron==0)&&(CHG_CHARGE==1)&&(CHG_FULL==1))
        {
sleep:
            // if(USE_UART) 
            // {
            //     printf("go to sleep...\r\n");
            // }
        
            //保存dly
            IRQ_ALL_DISABLE();
            FLASH_UnLock();
            //擦除
            FLASH_Erase(FLASH_DATA,0);       
            //写入数据
            FLASH_Write(FLASH_DATA,0,dly);
            FLASH_Lock();	
            IRQ_ALL_ENABLE(); 
        
            mode=0;
            isPoweron=0;
            batlow_cnt=0;batlow1_cnt=0;
            //rf_dat=0;rev_success=0;
            //RF_DIS;
            TMR_Stop(TMR0);
            LEDROFF;LEDGOFF;
            A1=0;A2=0;B1=0;B2=0;
            
            //direction=0;
            K1_cnt_EN=1;K1_cnt=0;K2_cnt_EN=1;K2_cnt=0;K3_cnt_EN=1;K3_cnt=0;
            //打开K2中断 CHG_CHARGE,CHG_FULL
            GPIO_EnableInt(GPIO1, GPIO_PIN_6_MSK);  // K2-P16
            GPIO_EnableInt(GPIO2, GPIO_PIN_4_MSK);  // CHG_CHARGE-P24
            GPIO_EnableInt(GPIO2, GPIO_PIN_5_MSK);  // CHG_FULL-P25
            delay1ms(100);				
            SYS_EnterStop();    //系统进入STOP低功耗模式

            //唤醒后要将中断关掉,防止在系统刚唤醒时这些中断引脚的状态不稳定导致误触发
            GPIO_DisableInt(GPIO1, GPIO_PIN_6_MSK);	// K2-P16
            GPIO_DisableInt(GPIO2, GPIO_PIN_4_MSK);  // CHG_CHARGE-P24
            GPIO_DisableInt(GPIO2, GPIO_PIN_5_MSK);  // CHG_FULL-P25
            if(USE_UART) 
            {
                printf("wakeup...\r\n");
            }

            if((K2==0)||(CHG_CHARGE==0)||(CHG_FULL==0))
            {
                delay1ms(100);	
                //RF_EN;
          
                //读dly
                IRQ_ALL_DISABLE();
                FLASH_UnLock();
                dly=FLASH_Read(FLASH_DATA,0);
                FLASH_Lock();
                IRQ_ALL_ENABLE();  
                if(dly==0xff) 
                {
                    dly=20;
                }
          
                break;
            }
        }
			
        if(isPoweron==0)
        {
            if((CHG_CHARGE==0)||(CHG_FULL==0))   //只充电未开机
            {
                batlow1=0;batlow1_cnt=0;
                batlow=0;batlow_cnt=0;
                while(1)
                {
                    LEDROFF;
                    if(CHG_CHARGE==0)
                    {							
                        if(ledonoff) 
                        {
                            LEDGON;
                        }
                        else 
                        {
                            LEDGOFF;
                        }
                    }
                    else
                    {
                        LEDGON;
                    }
						
                    if((CHG_CHARGE==1)&&(CHG_FULL==1)) 
                    {
                        break; //充电器拔掉
                    }
						
                    //充电时长按开机
                    if(K2_cnt>50)
                    {
                        K2_cnt_EN=0;K2_cnt=0;
                        isPoweron=1;
                        if(K1==0) 
                        {
                            mode=1;K1_cnt_EN=0;K1_cnt=0;
                        }
                        Get_BatV();
                        while(!K1);
                        delay1ms(100);
                        TMR_Start(TMR0);
                        break;
                    }
						
                    if(K2==1) 
                    {
                        K2_cnt=0;
                    }
                }
            }
				
            //长按开机(batlow1==0时)
            if(batlow1==0)
            {
                if(K2_cnt>50)
                {
                    K2_cnt=0;K2_cnt_EN=0;
                    isPoweron=1;
                    if(K1==0) 
                    {
                        mode=1;K1_cnt_EN=0;K1_cnt=0;
                    }
                    Get_BatV();
                    TMR_Start(TMR0);
                }
            }
        }
			
        if(isPoweron==1)
        {
            //LED显示
            if((CHG_CHARGE==1)&&(CHG_FULL==1))  //放电时LED显示
            {
                if(batlow==1)  //电压过低
                {
                    LEDGOFF;
                    if(motor_status==0) 
                    {
                        LEDRON;
                    }
                    else
                    {
                        if(ledonoff1==1) 
                        {
                            LEDRON;
                        }
                        else 
                        {
                            LEDROFF;
                        }
                    }
                }
                else          //电压正常
                {
                    if(mode==0)
                    {
                        LEDROFF;
                        if(motor_status==0) 
                        {
                            LEDGON;
                        }
                        else
                        {
                            if(ledonoff1==1) 
                            {
                                LEDGON;
                            }
                            else 
                            {
                                LEDGOFF;
                            }              
                        }
                    }
                    else
                    {
                        LEDGOFF;
                        if(motor_status==0) 
                        {
                            LEDRON;
                        }
                        else
                        {
                            if(ledonoff1==1) 
                            {
                                LEDRON;
                            }
                            else 
                            {
                                LEDROFF;
                            }                    
                        }
                    }
                }
          
                //放电时电压<3.1关机
                if(batlow1==1) 
                {
                    goto sleep;
                }
            }		

            if((CHG_CHARGE==0)||(CHG_FULL==0))  //充电时LED显示
            {
                batlow1=0;batlow1_cnt=0;
                batlow=0;batlow_cnt=0;
                LEDROFF;
                if(CHG_CHARGE==0)
                {
                    if(ledonoff==1) 
                    {
                        LEDGON;
                    }
                    else 
                    {
                        LEDGOFF;
                    }
                }
                else
                {
                    LEDGON;
                }
            }		
				
            //按键检测
            // 按键功能：K1-逆时针，K2-开关机，K3-顺时针
            if(mode==0)                    //正常模式点动
            {
                if((K1==0)&&(K3==1))    //K1按下-逆时针旋转(点动)
                {
                    motor_status=5;
                }
                else if((K1==1)&&(K3==0))   //K3按下-顺时针旋转(点动)
                {
                    motor_status=6;
                }
                else
                {
                    motor_status=0;
                }
            }
            else  // 测试模式
            {
                if((K1_cnt>3)&&(K1==1))  // K1短按-逆时针
                {
                    K1_cnt_EN=0;K1_cnt=0;
                    K1_num++;
                    if(K1_num>1)
                    {
                        K1_num=0;
                    }
                    if(K1_num==1)
                    {
                        K3_num=0;motor_status=5;  // 逆时针旋转
                    }
                }

                if((K3_cnt>3)&&(K3==1))  // K3短按-顺时针
                {
                    K3_cnt_EN=0;K3_cnt=0;
                    K3_num++;
                    if(K3_num>1)
                    {
                        K3_num=0;
                    }
                    if(K3_num==1)
                    {
                        K1_num=0;motor_status=6;  // 顺时针旋转
                    }
                }

                if((K1_num==0)&&(K3_num==0))
                {
                    motor_status=0;
                }
            }
   
            // //遥控接收
            // if(rev_success==1)
            // {		
            //     no_RF_signal=0;
            //     no_RF_signal_cnt=0;
          
            //     code_tmp1=(rf_dat&0x00ff0000)>>16;
            //     code_tmp2=(rf_dat&0x0000ff00)>>8;
            //     code_tmp3=rf_dat&0x000000f0;
            //     rev_success=0;	
					
            //     if(K1==0)
            //     {
            //         K1_cnt_EN=0;K1_cnt=0;
            //         RF_DM();
            //     }
            //     else
            //     {
            //         if((code_tmp1==code1)&&(code_tmp2==code2)&&(code_tmp3==code3))
            //         {	
            //             result=rf_dat&0x0f;
            //             if(USE_UART) 
            //             {
            //                 printf("result=%d\r\n",(uint16_t)result);
            //             }
            //             if(result!=0) 
            //             {
            //                 tmr1_cnt=TMR1_cnts;
            //             }
              
            //             if(result==10)   //正转快(点动)
            //             {
            //                 motor_status=7;
            //             }
              
            //             if(result==11)   //反转快(点动)
            //             {
            //                 motor_status=8;
            //             }
              
            //             if(result==12)   //正转慢(点动)
            //             {
            //                 motor_status=3;
            //             }
                
            //             if(result==13)   //反转满(点动)
            //             {
            //                 motor_status=4;
            //             }
              
            //             if(result==5)
            //             {
            //                 dly=dly-1;
            //                 if(dly<=1) 
            //                 {
            //                     dly=1;
            //                 }
            //                 LEDRON;
            //                 delay1ms(100);
            //                 LEDROFF;
            //             }
              
            //             if(result==6)
            //             {
            //                 dly=dly+1;
            //                 if(dly>=50) 
            //                 {
            //                     dly=50;
            //                 }
            //                 LEDRON;
            //                 delay1ms(100);
            //                 LEDROFF;
            //             }
            //         }
            //     }
          
            //     rf_dat=0;
            // }	  //end of 遥控接收
        
            //status 3,4 用于遥控点动
            //      正慢                反慢               正快               反快
            if((motor_status==3)||(motor_status==4)||(motor_status==7)||(motor_status==8))
            {
                // if(no_RF_signal==1)
                {
                    motor_status=0;
                    is_running=0;
                }
            }
          
            //马达控制
            switch(motor_status)
            {
                case 0:
                {
                    A1=1;B1=1;A2=1;B2=1;
                    break;
                }
                case 1: //正转
                {
                    if(direction!=1) 
                    {
                        speedup=1;
                    }
                    direction=1;
                
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    {
                        tmr4_cnt1=50;  //10
                    }			
                
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }  
                    break;
                }
                case 2:  //反转
                {
                    if(direction!=2) 
                    {
                        speedup=1;
                    }
                    direction=2;
						
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    {
                        tmr4_cnt1=50;  //10
                    }			
            
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }
                    break;
                }
                case 3:   //遥控正转慢(点动)
                {
                    if(direction!=1) 
                    {
                        speedup=1;
                    }
                    direction=1;
						
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    {
                        tmr4_cnt1=60;  //40
                    }							
            
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }
                    break;
                }
                case 4:  //遥控反转慢(点动)
                {
                    if(direction!=2) 
                    {
                        speedup=1;
                    }
                    direction=2;
						
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    {
                        tmr4_cnt1=60;  //40
                    }	          
          
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }
                    break; 
                }
                case 5:   //按键正转快(点动)
                {
                    if(direction!=1) 
                    {
                        speedup=1;
                    }
                    direction=1;
						
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    {
                        if(mode==1) 
                        {
                            tmr4_cnt1=1;
                        }
                        else 
                        {
                            tmr4_cnt1=10;   //10
                        }
                    }							
            
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }
                    break;
                }
                case 6:  //按键反转快(点动)
                {
                    if(direction!=2) 
                    {
                        speedup=1;
                    }
                    direction=2;
						
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    {
                        if(mode==1) 
                        {
                            tmr4_cnt1=1;
                        }
                        else 
                        {
                            tmr4_cnt1=10;  //10
                        }
                    }	          
          
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }
                    break; 
                }
                case 7:   //遥控正转快(点动)
                {
                    if(direction!=1) 
                    {
                        speedup=1;
                    }
                    direction=1;
						
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    { 
                        tmr4_cnt1=10;   //10
                    }							
            
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }
                    break;
                }
                case 8:  //遥控反转快(点动)
                {
                    if(direction!=2) 
                    {
                        speedup=1;
                    }
                    direction=2;
						
                    if(speedup==1) 
                    {
                        tmr4_cnt1=1;
                    }
                    else
                    {
                        tmr4_cnt1=10;   //10
                    }	          
          
                    switch(interval_cnt)
                    {
                        case 0:
                        {
                            A1=1;B1=1;A2=1;B2=0;
                            break;
                        }
                        case 1:
                        {
                            A1=1;B1=1;A2=0;B2=1;
                            break;
                        }
                        case 2:
                        {
                            A1=1;B1=0;A2=1;B2=1;
                            break;
                        }
                        case 3:
                        {
                            A1=0;B1=1;A2=1;B2=1;
                            break;
                        }
                        default:
                        {
                            break;
                        }
                    }
                    break; 
                }
                default:
                {
                    break;
                }
            }
				
            //长按关机
            if(K2_cnt>150)
            {
                K2_cnt=0;K2_cnt_EN=0;
                LEDROFF;LEDGOFF;
                while(!K1);
                delay1ms(100);
                isPoweron=0;
            }
				
            //定时电压检测
            if(canADC)
            {
                Get_BatV();
                canADC=0;
          
                if(BatV<3.5)
                {
                    batlow_cnt++;
                    if(batlow_cnt>5) 
                    {
                        batlow=1;
                    }
                }
                else
                {
                    batlow=0;batlow_cnt=0;
                }
          
                if(BatV<3.1)
                {
                    batlow1_cnt++;
                    if(batlow1_cnt>5) 
                    {
                        batlow1=1;
                    }
                }
                else
                {
                    batlow1_cnt=0;batlow1=0;
                }
            }
        }
    }		
}

void delay1ms(unsigned int z)  
{
 unsigned int x,y;
 for(y=0;y<z;y++)
 for(x=0;x<=2000;x++);
}

void delay1us(unsigned int z)   //16MHz时,约1.1us
{
	unsigned int x;
	for(x=0;x<z;x++);	
}

// void RF_DM()
// {
// 	uint8_t i,RF_Code[3];
	
//   LEDGOFF;
// 	RF_Code[0]=(rf_dat>>16)&0xff;
// 	RF_Code[1]=(rf_dat>>8)&0xff;
// 	RF_Code[2]=rf_dat&0xf0;
	
// 	IRQ_ALL_DISABLE();
// 	FLASH_UnLock();
// 	//擦除
// 	for(i=0;i<3;i++)
// 	{
// 		FLASH_Erase(FLASH_DATA,RF_code_addr+i);
// 	}
// 	//写入数据
// 	for(i=0;i<3;i++)
// 	{
// 		FLASH_Write(FLASH_DATA,RF_code_addr+i,RF_Code[i]);
// 	}
// 	//读出3个数据
// 	code1=FLASH_Read(FLASH_DATA,RF_code_addr);
// 	code2=FLASH_Read(FLASH_DATA,RF_code_addr+1);
// 	code3=FLASH_Read(FLASH_DATA,RF_code_addr+2);	
// 	FLASH_Lock();	
// 	IRQ_ALL_ENABLE();
// 	if((code1==RF_Code[0])&&(code2==RF_Code[1])&&(code3==RF_Code[2]))
// 	{
// 		LEDRON;delay1ms(200);LEDROFF;delay1ms(200);LEDRON;delay1ms(200);LEDROFF;delay1ms(200);LEDRON;delay1ms(200);LEDROFF;delay1ms(200);
// 		//if(USE_UART) printf("code1=%d,code2=%d,code3=%d\r\n",code1,code2,code3);
// 	}
// 	while(!K1);
// 	delay1ms(100);	
// }

void Get_BatV()
{
	float adc_result;
	uint16_t result[6];
	uint16_t i;
	
	for(i=0;i<6;i++)
	{
		ADC_GO();
		while(ADC_IS_BUSY);
		result[i]=ADC_GetADCResult();		
	}
	adc_result=(result[2]+result[3]+result[4]+result[5])/4.0;
	
	BatV=4915.2/adc_result;   //1.2*4096/adc_result
	
	//if(USE_UART) printf("BatV=%5.2f\r\n",BatV);
}