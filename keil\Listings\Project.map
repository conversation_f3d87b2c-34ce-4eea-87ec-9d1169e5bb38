LX51 LINKER/LOCATER V4.66.97.0                                                          07/21/2025  15:36:55  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\gpio.obj, .\Objects\system.obj, .\Objects\uart.ob
>> j, .\Objects\timer.obj, .\Objects\flash.obj, .\Objects\adc.obj, .\Objects\isr.obj, .\Objects\main.obj, .\Objects\demo
>> _gpio.obj, .\Objects\demo_uart.obj, .\Objects\demo_timer.obj, .\Objects\demo_adc.obj, .\Objects\eeprom.obj TO .\Objec
>> ts\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\demo_gpio.obj (DEMO_GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\demo_uart.obj (DEMO_UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\demo_timer.obj (DEMO_TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\demo_adc.obj (DEMO_ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\eeprom.obj (EEPROM)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCMP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?CASTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 2


  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?IMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (ABS)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   001615H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   000033H   XDATA
C:000000H   C:000000H   C:00FFFFH             CONST
I:000020H.0 I:000020H.0 I:00002FH.7 000001H.7 BIT
I:000000H   I:000000H   I:00007FH   000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H.0 00001FH.7 000018H.0 ---    ---      **GAP**
000020H.0 000021H.6 000001H.7 BIT    UNIT     BIT            ?BI?MAIN
000021H.7 000021H   000000H.1 ---    ---      **GAP**
000022H   000022H   000001H   BYTE   UNIT     IDATA          ?STACK

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 3


00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000026H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000027H   000027H   000001H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
000028H   000028H   000001H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
000038H   00003AH   000003H   ---    ---      **GAP**
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   000042H   000005H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000061H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   00008CH   000007H   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
00008DH   000092H   000006H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   00086BH   0007B6H   BYTE   UNIT     CODE           ?C?LIB_CODE
00086CH   000E05H   00059AH   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
000E06H   000F67H   000162H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
000F68H   001032H   0000CBH   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?DEMO_GPIO
001033H   0010D4H   0000A2H   BYTE   UNIT     CODE           ?PR?GET_BATV?MAIN
0010D5H   00116EH   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
00116FH   0011F4H   000086H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
0011F5H   00124EH   00005AH   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
00124FH   0012A6H   000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
0012A7H   0012F2H   00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
0012F3H   00133EH   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
00133FH   001388H   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
001389H   0013D1H   000049H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
0013D2H   001405H   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
001406H   001438H   000033H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
001439H   001468H   000030H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
001469H   001492H   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?DEMO_TIMER
001493H   0014BBH   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?DEMO_TIMER
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 4


0014BCH   0014E4H   000029H   BYTE   UNIT     CODE           ?PR?TMR4_CONFIG?DEMO_TIMER
0014E5H   00150BH   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
00150CH   001530H   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
001531H   001554H   000024H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
001555H   001576H   000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
001577H   001595H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
001596H   0015B4H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
0015B5H   0015D2H   00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
0015D3H   0015F0H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
0015F1H   00160DH   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
00160EH   001623H   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
001624H   001637H   000014H   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?DEMO_ADC
001638H   001646H   00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
001647H   001655H   00000FH   BYTE   UNIT     CODE           ?C_INITSEG

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000015H   000016H   BYTE   UNIT     XDATA          ?XD?MAIN
000016H   000024H   00000FH   BYTE   UNIT     XDATA          ?XD?ISR
000025H   000032H   00000EH   BYTE   UNIT     XDATA          _XDATA_GROUP_

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001BH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001BH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001BH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001BH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001BH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           00027AH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000011H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
   *DEL*:           000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 5


   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
   *DEL*:           000012H   BYTE   UNIT     CODE           ?PR?_DELAY1US?MAIN
   *DEL*:           00000CH   BYTE   UNIT     CONST          ?CO?MAIN
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GPIO_UART0_SLEEP?DEMO_UART
   *DEL*:           00004BH   BYTE   UNIT     CODE           ?PR?UART0_CONFIG?DEMO_UART
   *DEL*:           000004H   BYTE   UNIT     XDATA          ?XD?UART0_CONFIG?DEMO_UART
   *DEL*:           00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?DEMO_UART
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?DEMO_UART
   *DEL*:           000038H   BYTE   UNIT     CODE           ?PR?_PUTS?DEMO_UART
   *DEL*:           000003H   BYTE   UNIT     XDATA          ?XD?_PUTS?DEMO_UART
   *DEL*:           000054H   BYTE   UNIT     CODE           ?PR?_EEPROM_WRITE_F?EEPROM
   *DEL*:           00000AH   BYTE   UNIT     XDATA          ?XD?_EEPROM_WRITE_F?EEPROM
   *DEL*:           000051H   BYTE   UNIT     CODE           ?PR?_EEPROM_READ_F?EEPROM
   *DEL*:           000006H   BYTE   UNIT     XDATA          ?XD?_EEPROM_READ_F?EEPROM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 6


   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_EEPROM_WRITE?EEPROM
   *DEL*:           000002H   BYTE   UNIT     XDATA          ?XD?_EEPROM_WRITE?EEPROM
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_EEPROM_READ?EEPROM
   *DEL*:           000002H   BYTE   UNIT     XDATA          ?XD?_EEPROM_READ?EEPROM
   *DEL*:           00009DH   BYTE   UNIT     CODE           ?PR?_EEPROM_WRITE16?EEPROM
   *DEL*:           000003H   BYTE   UNIT     XDATA          ?XD?_EEPROM_WRITE16?EEPROM
   *DEL*:           000083H   BYTE   UNIT     CODE           ?PR?_EEPROM_READ16?EEPROM
   *DEL*:           000006H   BYTE   UNIT     XDATA          ?XD?_EEPROM_READ16?EEPROM
   *DEL*:           00002AH   BYTE   UNIT     CODE           ?PR?FLASH_DATA_ERASE?EEPROM
   *DEL*:           000001H   BYTE   UNIT     XDATA          ?XD?FLASH_DATA_ERASE?EEPROM
   *DEL*:           000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
   *DEL*:           000005H   BYTE   UNIT     DATA           ?DT?PRINTF?PRINTF
   *DEL*:           000001H.1 BIT    UNIT     BIT            ?BI?PRINTF?PRINTF
   *DEL*:           000030H   BYTE   UNIT     XDATA          ?XD?PRINTF?PRINTF
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_ABS?ABS



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                        XDATA_GROUP
--> CALLED FUNCTION/MODULE             START  STOP
==================================================
?C_C51STARTUP                          ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                              ----- -----
  +--> GPIO_CONFIG/DEMO_GPIO
  +--> TMR0_CONFIG/DEMO_TIMER
  +--> TMR1_CONFIG/DEMO_TIMER
  +--> TMR4_CONFIG/DEMO_TIMER
  +--> ADC_CONFIG/DEMO_ADC
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> FLASH_UNLOCK/FLASH
  +--> FLASH_LOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> _TMR_STOP/TIMER
  +--> _GPIO_ENABLEINT/GPIO
  +--> _DELAY1MS/MAIN
  +--> SYS_ENTERSTOP/SYSTEM
  +--> _GPIO_DISABLEINT/GPIO
  +--> GET_BATV/MAIN
  +--> _TMR_START/TIMER

GPIO_CONFIG/DEMO_GPIO                  ----- -----
  +--> _GPIO_ENABLEINT/GPIO

_GPIO_ENABLEINT/GPIO                   ----- -----

TMR0_CONFIG/DEMO_TIMER                 ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER               ----- -----

_TMR_CONFIGTIMERCLK/TIMER              ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER           ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 7



_TMR_ENABLEOVERFLOWINT/TIMER           ----- -----

_TMR_START/TIMER                       ----- -----

TMR1_CONFIG/DEMO_TIMER                 ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

TMR4_CONFIG/DEMO_TIMER                 ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

ADC_CONFIG/DEMO_ADC                    ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGAN31/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                 ----- -----

_ADC_ENABLECHANNEL/ADC                 ----- -----

_ADC_CONFIGAN31/ADC                    ----- -----

ADC_START/ADC                          ----- -----

SYS_ENABLEWAKEUP/SYSTEM                ----- -----

FLASH_UNLOCK/FLASH                     ----- -----

FLASH_LOCK/FLASH                       ----- -----

_FLASH_READ/FLASH                      ----- -----

_FLASH_ERASE/FLASH                     ----- -----

_FLASH_WRITE/FLASH                     ----- -----

_TMR_STOP/TIMER                        ----- -----

_DELAY1MS/MAIN                         ----- -----

SYS_ENTERSTOP/SYSTEM                   ----- -----

_GPIO_DISABLEINT/GPIO                  ----- -----

GET_BATV/MAIN                          0025H 0032H
  +--> ADC_GETADCRESULT/ADC

ADC_GETADCRESULT/ADC                   ----- -----

?C_INITSEG                             ----- -----

*** NEW ROOT *************************

INT0_IRQHANDLER/ISR                    ----- -----

*** NEW ROOT *************************
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 8



TIMER0_IRQHANDLER/ISR                  ----- -----

*** NEW ROOT *************************

INT1_IRQHANDLER/ISR                    ----- -----

*** NEW ROOT *************************

TIMER1_IRQHANDLER/ISR                  ----- -----

*** NEW ROOT *************************

UART0_IRQHANDLER/ISR                   ----- -----

*** NEW ROOT *************************

TIMER2_IRQHANDLER/ISR                  ----- -----

*** NEW ROOT *************************

UART1_IRQHANDLER/ISR                   ----- -----

*** NEW ROOT *************************

P0EI_IRQHANDLER/ISR                    ----- -----
  +--> _GPIO_GETINTFLAG/GPIO
  +--> _GPIO_CLEARINTFLAG/GPIO

_GPIO_GETINTFLAG/GPIO                  ----- -----

_GPIO_CLEARINTFLAG/GPIO                ----- -----

*** NEW ROOT *************************

P1EI_IRQHANDLER/ISR                    ----- -----

*** NEW ROOT *************************

P2EI_IRQHANDLER/ISR                    ----- -----

*** NEW ROOT *************************

P3EI_IRQHANDLER/ISR                    ----- -----
  +--> _GPIO_GETINTFLAG/GPIO
  +--> _GPIO_CLEARINTFLAG/GPIO

*** NEW ROOT *************************

LVD_IRQHANDLER/ISR                     ----- -----

*** NEW ROOT *************************

LSE_IRQHANDLER/ISR                     ----- -----

*** NEW ROOT *************************

ACMP_IRQHANDLER/ISR                    ----- -----

*** NEW ROOT *************************

TIMER3_IRQHANDLER/ISR                  ----- -----

*** NEW ROOT *************************

LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 9


TIMER4_IRQHANDLER/ISR                  ----- -----

*** NEW ROOT *************************

EPWM_IRQHANDLER/ISR                    ----- -----

*** NEW ROOT *************************

ADC_IRQHANDLER/ISR                     ----- -----

*** NEW ROOT *************************

WDT_IRQHANDLER/ISR                     ----- -----

*** NEW ROOT *************************

I2C_IRQHANDLER/ISR                     ----- -----

*** NEW ROOT *************************

SPI_IRQHANDLER/ISR                     ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_EEPROM_Write_f?BYTE
*DEL*:00000000H   XDATA    ---       ?_PRINTF?BYTE
*DEL*:00000000H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      01000305H   CODE     ---       ?C?CASTF
      01000846H   CODE     ---       ?C?CCASE
      01000657H   CODE     ---       ?C?CLDOPTR
      0100063EH   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      01000684H   CODE     ---       ?C?CSTPTR
      010002D1H   CODE     ---       ?C?FCASTC
      010002CCH   CODE     ---       ?C?FCASTI
      010002C7H   CODE     ---       ?C?FCASTL
      010000BDH   CODE     ---       ?C?FPADD
      01000250H   CODE     ---       ?C?FPCMP
      0100024EH   CODE     ---       ?C?FPCMP3
      010003C3H   CODE     ---       ?C?FPCONVERT
      010001B1H   CODE     ---       ?C?FPDIV
      0100033CH   CODE     ---       ?C?FPGETOPN2
      010004C8H   CODE     ---       ?C?FPMUL
      01000371H   CODE     ---       ?C?FPNANRESULT
      0100037BH   CODE     ---       ?C?FPOVERFLOW
      01000353H   CODE     ---       ?C?FPRESULT
      01000367H   CODE     ---       ?C?FPRESULT2
      01000386H   CODE     ---       ?C?FPROUND
      010000B9H   CODE     ---       ?C?FPSUB
      01000378H   CODE     ---       ?C?FPUNDERFLOW
      010005FEH   CODE     ---       ?C?FTNPWR
      010006A8H   CODE     ---       ?C?ILDIX
      01000696H   CODE     ---       ?C?IMUL
      010006BEH   CODE     ---       ?C?LMUL
      010007DBH   CODE     ---       ?C?LNEG
      010007F5H   CODE     ---       ?C?LSTKXDATA
      010007E9H   CODE     ---       ?C?LSTXDATA
      01000826H   CODE     ---       ?C?PLDIXDATA
      0100083DH   CODE     ---       ?C?PSTXDATA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 10


      01000749H   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      0100112AH   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
*DEL*:00000000H   CODE     ---       _ABS
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCVref
      01001638H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      0100160EH   CODE     ---       _ADC_ConfigRunMode
      010015D3H   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      01001535H   CODE     ---       _delay1ms
*DEL*:00000000H   CODE     ---       _delay1us
*DEL*:00000000H   CODE     ---       _EEPROM_Read
*DEL*:00000000H   CODE     ---       _EEPROM_Read16
*DEL*:00000000H   CODE     ---       _EEPROM_Read_f
*DEL*:00000000H   CODE     ---       _EEPROM_Write
*DEL*:00000000H   CODE     ---       _EEPROM_Write16
*DEL*:00000000H   CODE     ---       _EEPROM_Write_f
      01001439H   CODE     ---       _FLASH_Erase
      01001406H   CODE     ---       _FLASH_Read
      010013D2H   CODE     ---       _FLASH_Write
      010012A7H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
      01001555H   CODE     ---       _GPIO_DisableInt
      010015B5H   CODE     ---       _GPIO_EnableInt
      0100124FH   CODE     ---       _GPIO_GetIntFlag
*DEL*:0000006BH   CODE     ---       _PRINTF
*DEL*:00000000H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
*DEL*:00000065H   CODE     ---       _SPRINTF
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      010012F3H   CODE     ---       _TMR_ConfigRunMode
      0100133FH   CODE     ---       _TMR_ConfigTimerClk
      010014E5H   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      010015F1H   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      01001577H   CODE     ---       _TMR_Start
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 11


      01001596H   CODE     ---       _TMR_Stop
*DEL*:00000000H   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
*DEL*:00000000H   CODE     ---       _UART_ConfigBRTClk
*DEL*:00000000H   CODE     ---       _UART_ConfigBRTPeriod
*DEL*:00000000H   CODE     ---       _UART_ConfigRunMode
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
*DEL*:00000000H   CODE     ---       _UART_EnableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_EnableInt
*DEL*:00000000H   CODE     ---       _UART_EnableReceive
*DEL*:00000000H   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
*DEL*:00000000H   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      0100002EH   CODE     ---       ACMP_IRQHandler
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      01001624H   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      0100150CH   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000031H   CODE     ---       ADC_IRQHandler
      0100001EH   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.3 BIT      BIT       batlow
      00000021H.0 BIT      BIT       batlow1
      02000011H   XDATA    BYTE      batlow1_cnt
      0200000DH   XDATA    BYTE      batlow_cnt
      02000012H   XDATA    FLOAT     BatV
*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
      00000020H.0 BIT      BIT       can_RF_change_status
      0200001BH   XDATA    BYTE      can_RF_change_status_cnt
      00000021H.6 BIT      BIT       canADC
      0200001AH   XDATA    BYTE      canADC_cnt
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 12


*SFR* 0000008EH   DATA     BYTE      CKCON
*SFR* 0000008FH   DATA     BYTE      CLKDIV
*SFR* 000000D0H.7 DATA     BIT       CY
      0200000FH   XDATA    BYTE      direction
      02000010H   XDATA    BYTE      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
      01000030H   CODE     ---       EPWM_IRQHandler
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
*DEL*:00000000H   CODE     ---       FLASH_DATA_Erase
      01000016H   CODE     ---       FLASH_Lock
      0100000EH   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      01001033H   CODE     ---       Get_BatV
*DEL*:00000000H   CODE     ---       getchar
      01000F68H   CODE     ---       GPIO_Config
*DEL*:00000000H   CODE     ---       GPIO_UART0_Sleep
      01000036H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
      02000016H   XDATA    WORD      interval_cnt
*SFR* 000000B8H   DATA     BYTE      IP
      00000021H.1 BIT      BIT       is_running
      00000021H.2 BIT      BIT       isPoweron
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      02000006H   XDATA    BYTE      K1_cnt
      00000020H.4 BIT      BIT       K1_cnt_EN
      0200000BH   XDATA    BYTE      K1_num
      02000021H   XDATA    BYTE      K1_release_cnt
      02000009H   XDATA    BYTE      K2_cnt
      00000020H.5 BIT      BIT       K2_cnt_EN
      02000022H   XDATA    BYTE      K2_release_cnt
      0200000AH   XDATA    BYTE      K3_cnt
      00000020H.6 BIT      BIT       K3_cnt_EN
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 13


      0200000CH   XDATA    BYTE      K3_num
      02000023H   XDATA    BYTE      K3_release_cnt
      00000020H.2 BIT      BIT       keydown
      00000021H.3 BIT      BIT       ledonoff
      00000020H.1 BIT      BIT       ledonoff1
      0200001CH   XDATA    BYTE      ledonoff1_cnt
      02000024H   XDATA    BYTE      ledonoff_cnt
      00000021H.4 BIT      BIT       longhit
      02000004H   XDATA    WORD      longhit_cnt
      0100002AH   CODE     ---       LSE_IRQHandler
      01000029H   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      0100086CH   CODE     ---       main
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      00000020H.7 BIT      BIT       mode
      0200000EH   XDATA    BYTE      motor_status
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01001389H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01000027H   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01000028H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 14


*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      010011F5H   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      02000007H   XDATA    WORD      result
*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000021H.5 BIT      BIT       speedup
      02000018H   XDATA    WORD      speedup_cnt
      01000037H   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      01000006H   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000056H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      02000000H   XDATA    DWORD     SystemClock
*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 15


*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      01000086H   CODE     ---       Timer0_IRQHandler
      01000E06H   CODE     ---       Timer1_IRQHandler
      01000022H   CODE     ---       Timer2_IRQHandler
      0100002FH   CODE     ---       Timer3_IRQHandler
      0100116FH   CODE     ---       Timer4_IRQHandler
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      01001469H   CODE     ---       TMR0_Config
      01001493H   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
      0200001DH   XDATA    INT       tmr4_cnt
      0200001FH   XDATA    INT       tmr4_cnt1
      010014BCH   CODE     ---       TMR4_Config
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
*DEL*:00000000H   CODE     ---       UART0_Config
      0100001AH   CODE     ---       UART0_IRQHandler
      01000026H   CODE     ---       UART1_IRQHandler
*DEL*:00000000H   CODE     ---       UART_DisableBRT
*DEL*:00000000H   CODE     ---       UART_EnableBRT
*SFR* 00000097H   DATA     BYTE      WDCON
      01000032H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 16


      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      010010D8H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      010010D5H   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      010010E3H   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      010010D5H   LINE      CODE     ---       #133
      010010D7H   LINE      CODE     ---       #134
      010010D8H   LINE      CODE     ---       #135
      010010D9H   LINE      CODE     ---       #136
      010010DBH   LINE      CODE     ---       #140
      010010DEH   LINE      CODE     ---       #141
      010010E0H   LINE      CODE     ---       #145
      010010E2H   LINE      CODE     ---       #147
      010010E3H   LINE      CODE     ---       #148
      010010E4H   LINE      CODE     ---       #149
      010010E5H   LINE      CODE     ---       #150
      010010E7H   LINE      CODE     ---       #151
      010010E9H   LINE      CODE     ---       #185
      010010ECH   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       GPIO
      010012A7H   PUBLIC    CODE     ---       _GPIO_ClearIntFlag
      0100124FH   PUBLIC    CODE     ---       _GPIO_GetIntFlag
      01001555H   PUBLIC    CODE     ---       _GPIO_DisableInt
      010015B5H   PUBLIC    CODE     ---       _GPIO_EnableInt
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 17


      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 18


      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 19


      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode

      010015B5H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      010015B5H   LINE      CODE     ---       #194
      010015B5H   LINE      CODE     ---       #195
      010015B5H   LINE      CODE     ---       #196
      010015C3H   LINE      CODE     ---       #197
      010015C3H   LINE      CODE     ---       #198
      010015C3H   LINE      CODE     ---       #199
      010015C6H   LINE      CODE     ---       #200
      010015C7H   LINE      CODE     ---       #201
      010015C7H   LINE      CODE     ---       #202
      010015CAH   LINE      CODE     ---       #203
      010015CBH   LINE      CODE     ---       #204
      010015CBH   LINE      CODE     ---       #205
      010015CEH   LINE      CODE     ---       #206
      010015CFH   LINE      CODE     ---       #207
      010015CFH   LINE      CODE     ---       #208
      010015D2H   LINE      CODE     ---       #209
      010015D2H   LINE      CODE     ---       #210
      010015D2H   LINE      CODE     ---       #211
      010015D2H   LINE      CODE     ---       #212
      010015D2H   LINE      CODE     ---       #213
      ---         BLOCKEND  ---      ---       LVL=0

      01001555H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      01001555H   LINE      CODE     ---       #226
      01001555H   LINE      CODE     ---       #227
      01001555H   LINE      CODE     ---       #228
      01001563H   LINE      CODE     ---       #229
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 20


      01001563H   LINE      CODE     ---       #230
      01001563H   LINE      CODE     ---       #231
      01001567H   LINE      CODE     ---       #232
      01001568H   LINE      CODE     ---       #233
      01001568H   LINE      CODE     ---       #234
      0100156CH   LINE      CODE     ---       #235
      0100156DH   LINE      CODE     ---       #236
      0100156DH   LINE      CODE     ---       #237
      01001571H   LINE      CODE     ---       #238
      01001572H   LINE      CODE     ---       #239
      01001572H   LINE      CODE     ---       #240
      01001576H   LINE      CODE     ---       #241
      01001576H   LINE      CODE     ---       #242
      01001576H   LINE      CODE     ---       #243
      01001576H   LINE      CODE     ---       #244
      01001576H   LINE      CODE     ---       #245
      ---         BLOCKEND  ---      ---       LVL=0

      0100124FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      0100124FH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      ---         BLOCKEND  ---      ---       LVL=1
      0100124FH   LINE      CODE     ---       #260
      0100124FH   LINE      CODE     ---       #261
      0100124FH   LINE      CODE     ---       #262
      01001251H   LINE      CODE     ---       #263
      0100125FH   LINE      CODE     ---       #264
      0100125FH   LINE      CODE     ---       #265
      0100125FH   LINE      CODE     ---       #266
      0100126DH   LINE      CODE     ---       #267
      0100126FH   LINE      CODE     ---       #268
      0100126FH   LINE      CODE     ---       #269
      0100127DH   LINE      CODE     ---       #270
      0100127FH   LINE      CODE     ---       #271
      0100127FH   LINE      CODE     ---       #272
      0100128DH   LINE      CODE     ---       #273
      0100128FH   LINE      CODE     ---       #274
      0100128FH   LINE      CODE     ---       #275
      0100129FH   LINE      CODE     ---       #276
      0100129FH   LINE      CODE     ---       #277
      0100129FH   LINE      CODE     ---       #278
      0100129FH   LINE      CODE     ---       #279
      0100129FH   LINE      CODE     ---       #280
      010012A6H   LINE      CODE     ---       #281
      ---         BLOCKEND  ---      ---       LVL=0

      010012A7H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      010012A7H   LINE      CODE     ---       #294
      010012A7H   LINE      CODE     ---       #295
      010012A7H   LINE      CODE     ---       #296
      010012B5H   LINE      CODE     ---       #297
      010012B5H   LINE      CODE     ---       #298
      010012B5H   LINE      CODE     ---       #299
      010012C3H   LINE      CODE     ---       #300
      010012C4H   LINE      CODE     ---       #301
      010012C4H   LINE      CODE     ---       #302
      010012D2H   LINE      CODE     ---       #303
      010012D3H   LINE      CODE     ---       #304
      010012D3H   LINE      CODE     ---       #305
      010012E1H   LINE      CODE     ---       #306
      010012E2H   LINE      CODE     ---       #307
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 21


      010012E2H   LINE      CODE     ---       #308
      010012F2H   LINE      CODE     ---       #309
      010012F2H   LINE      CODE     ---       #310
      010012F2H   LINE      CODE     ---       #311
      010012F2H   LINE      CODE     ---       #312
      010012F2H   LINE      CODE     ---       #313
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SYSTEM
      01000056H   PUBLIC    CODE     ---       SYS_EnterStop
      01000006H   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 22


      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 23


      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #328
      01000006H   LINE      CODE     ---       #329
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 24


      01000006H   LINE      CODE     ---       #330
      01000009H   LINE      CODE     ---       #331
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      01000056H   LINE      CODE     ---       #353
      01000056H   LINE      CODE     ---       #354
      01000056H   LINE      CODE     ---       #355
      01000057H   LINE      CODE     ---       #356
      01000058H   LINE      CODE     ---       #357
      0100005BH   LINE      CODE     ---       #358
      0100005CH   LINE      CODE     ---       #359
      0100005DH   LINE      CODE     ---       #360
      0100005EH   LINE      CODE     ---       #361
      0100005FH   LINE      CODE     ---       #362
      01000060H   LINE      CODE     ---       #363
      01000061H   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       UART
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 25


      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 26


      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 27


      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000003H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      BaudRateTimer
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      BRTPeriod

      ---         MODULE    ---      ---       TIMER
      01001596H   PUBLIC    CODE     ---       _TMR_Stop
      01001577H   PUBLIC    CODE     ---       _TMR_Start
      010015F1H   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      010014E5H   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      0100133FH   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      010012F3H   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 28


      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 29


      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 30


      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010012F3H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      010012F3H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010012F3H   LINE      CODE     ---       #74
      010012F3H   LINE      CODE     ---       #75
      010012F3H   LINE      CODE     ---       #76
      010012F3H   LINE      CODE     ---       #78
      01001302H   LINE      CODE     ---       #79
      01001302H   LINE      CODE     ---       #80
      01001302H   LINE      CODE     ---       #81
      01001304H   LINE      CODE     ---       #82
      01001308H   LINE      CODE     ---       #83
      0100130EH   LINE      CODE     ---       #84
      0100130EH   LINE      CODE     ---       #85
      01001310H   LINE      CODE     ---       #86
      01001310H   LINE      CODE     ---       #87
      01001312H   LINE      CODE     ---       #88
      01001316H   LINE      CODE     ---       #89
      01001323H   LINE      CODE     ---       #90
      01001325H   LINE      CODE     ---       #91
      01001326H   LINE      CODE     ---       #92
      01001326H   LINE      CODE     ---       #93
      01001328H   LINE      CODE     ---       #94
      0100132BH   LINE      CODE     ---       #95
      0100132CH   LINE      CODE     ---       #96
      0100132EH   LINE      CODE     ---       #97
      0100132FH   LINE      CODE     ---       #98
      0100132FH   LINE      CODE     ---       #99
      01001331H   LINE      CODE     ---       #100
      01001335H   LINE      CODE     ---       #101
      0100133CH   LINE      CODE     ---       #102
      0100133EH   LINE      CODE     ---       #103
      0100133EH   LINE      CODE     ---       #104
      0100133EH   LINE      CODE     ---       #105
      0100133EH   LINE      CODE     ---       #106
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 31


      0100133EH   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      0100133FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      0100133FH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100133FH   LINE      CODE     ---       #117
      0100133FH   LINE      CODE     ---       #118
      0100133FH   LINE      CODE     ---       #119
      0100133FH   LINE      CODE     ---       #121
      0100134EH   LINE      CODE     ---       #122
      0100134EH   LINE      CODE     ---       #123
      0100134EH   LINE      CODE     ---       #124
      01001350H   LINE      CODE     ---       #125
      01001354H   LINE      CODE     ---       #126
      0100135AH   LINE      CODE     ---       #127
      0100135AH   LINE      CODE     ---       #128
      0100135CH   LINE      CODE     ---       #129
      0100135CH   LINE      CODE     ---       #130
      0100135EH   LINE      CODE     ---       #131
      01001362H   LINE      CODE     ---       #132
      01001367H   LINE      CODE     ---       #133
      01001369H   LINE      CODE     ---       #134
      0100136AH   LINE      CODE     ---       #135
      0100136AH   LINE      CODE     ---       #136
      0100136CH   LINE      CODE     ---       #137
      01001370H   LINE      CODE     ---       #138
      01001375H   LINE      CODE     ---       #139
      01001375H   LINE      CODE     ---       #140
      01001377H   LINE      CODE     ---       #141
      01001377H   LINE      CODE     ---       #142
      01001379H   LINE      CODE     ---       #143
      0100137DH   LINE      CODE     ---       #144
      01001386H   LINE      CODE     ---       #145
      01001388H   LINE      CODE     ---       #146
      01001388H   LINE      CODE     ---       #147
      01001388H   LINE      CODE     ---       #148
      01001388H   LINE      CODE     ---       #149
      01001388H   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      010014E5H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      010014E5H   LINE      CODE     ---       #160
      010014E5H   LINE      CODE     ---       #161
      010014E5H   LINE      CODE     ---       #162
      010014F4H   LINE      CODE     ---       #163
      010014F4H   LINE      CODE     ---       #164
      010014F4H   LINE      CODE     ---       #165
      010014F6H   LINE      CODE     ---       #166
      010014F8H   LINE      CODE     ---       #167
      010014F9H   LINE      CODE     ---       #168
      010014F9H   LINE      CODE     ---       #169
      010014FBH   LINE      CODE     ---       #170
      010014FDH   LINE      CODE     ---       #171
      010014FEH   LINE      CODE     ---       #172
      010014FEH   LINE      CODE     ---       #173
      01001500H   LINE      CODE     ---       #174
      01001502H   LINE      CODE     ---       #175
      01001503H   LINE      CODE     ---       #176
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 32


      01001503H   LINE      CODE     ---       #177
      01001507H   LINE      CODE     ---       #178
      0100150BH   LINE      CODE     ---       #179
      0100150BH   LINE      CODE     ---       #180
      0100150BH   LINE      CODE     ---       #181
      0100150BH   LINE      CODE     ---       #182
      0100150BH   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      010015F1H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010015F1H   LINE      CODE     ---       #256
      010015F1H   LINE      CODE     ---       #257
      010015F1H   LINE      CODE     ---       #258
      01001600H   LINE      CODE     ---       #259
      01001600H   LINE      CODE     ---       #260
      01001600H   LINE      CODE     ---       #261
      01001602H   LINE      CODE     ---       #262
      01001603H   LINE      CODE     ---       #263
      01001603H   LINE      CODE     ---       #264
      01001605H   LINE      CODE     ---       #265
      01001606H   LINE      CODE     ---       #266
      01001606H   LINE      CODE     ---       #267
      01001609H   LINE      CODE     ---       #268
      0100160AH   LINE      CODE     ---       #269
      0100160AH   LINE      CODE     ---       #270
      0100160DH   LINE      CODE     ---       #271
      0100160DH   LINE      CODE     ---       #272
      0100160DH   LINE      CODE     ---       #273
      0100160DH   LINE      CODE     ---       #274
      0100160DH   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      01001577H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01001577H   LINE      CODE     ---       #368
      01001577H   LINE      CODE     ---       #369
      01001577H   LINE      CODE     ---       #370
      01001586H   LINE      CODE     ---       #371
      01001586H   LINE      CODE     ---       #372
      01001586H   LINE      CODE     ---       #373
      01001589H   LINE      CODE     ---       #374
      0100158AH   LINE      CODE     ---       #375
      0100158AH   LINE      CODE     ---       #376
      0100158DH   LINE      CODE     ---       #377
      0100158EH   LINE      CODE     ---       #378
      0100158EH   LINE      CODE     ---       #379
      01001591H   LINE      CODE     ---       #380
      01001592H   LINE      CODE     ---       #381
      01001592H   LINE      CODE     ---       #382
      01001595H   LINE      CODE     ---       #383
      01001595H   LINE      CODE     ---       #384
      01001595H   LINE      CODE     ---       #385
      01001595H   LINE      CODE     ---       #386
      01001595H   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0

      01001596H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 33


      00000007H   SYMBOL    DATA     BYTE      Timern
      01001596H   LINE      CODE     ---       #395
      01001596H   LINE      CODE     ---       #396
      01001596H   LINE      CODE     ---       #397
      010015A5H   LINE      CODE     ---       #398
      010015A5H   LINE      CODE     ---       #399
      010015A5H   LINE      CODE     ---       #400
      010015A8H   LINE      CODE     ---       #401
      010015A9H   LINE      CODE     ---       #402
      010015A9H   LINE      CODE     ---       #403
      010015ACH   LINE      CODE     ---       #404
      010015ADH   LINE      CODE     ---       #405
      010015ADH   LINE      CODE     ---       #406
      010015B0H   LINE      CODE     ---       #407
      010015B1H   LINE      CODE     ---       #408
      010015B1H   LINE      CODE     ---       #409
      010015B4H   LINE      CODE     ---       #410
      010015B4H   LINE      CODE     ---       #411
      010015B4H   LINE      CODE     ---       #412
      010015B4H   LINE      CODE     ---       #413
      010015B4H   LINE      CODE     ---       #414
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       FLASH
      01001439H   PUBLIC    CODE     ---       _FLASH_Erase
      01001406H   PUBLIC    CODE     ---       _FLASH_Read
      010013D2H   PUBLIC    CODE     ---       _FLASH_Write
      01000016H   PUBLIC    CODE     ---       FLASH_Lock
      0100000EH   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 34


      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 35


      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 36


      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #68
      0100000EH   LINE      CODE     ---       #69
      0100000EH   LINE      CODE     ---       #70
      01000011H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #79
      01000016H   LINE      CODE     ---       #80
      01000016H   LINE      CODE     ---       #81
      01000019H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      010013D2H   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      010013D2H   LINE      CODE     ---       #95
      010013D4H   LINE      CODE     ---       #96
      010013D4H   LINE      CODE     ---       #97
      010013D8H   LINE      CODE     ---       #98
      010013DAH   LINE      CODE     ---       #99
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 37


      010013DDH   LINE      CODE     ---       #101
      010013E0H   LINE      CODE     ---       #102
      010013E0H   LINE      CODE     ---       #103
      010013E2H   LINE      CODE     ---       #104
      010013E7H   LINE      CODE     ---       #105
      010013E8H   LINE      CODE     ---       #106
      010013E9H   LINE      CODE     ---       #107
      010013EAH   LINE      CODE     ---       #108
      010013EBH   LINE      CODE     ---       #109
      010013ECH   LINE      CODE     ---       #110
      010013EDH   LINE      CODE     ---       #111
      010013F2H   LINE      CODE     ---       #112
      010013F4H   LINE      CODE     ---       #113
      010013F5H   LINE      CODE     ---       #115
      010013F5H   LINE      CODE     ---       #116
      010013FAH   LINE      CODE     ---       #117
      010013FBH   LINE      CODE     ---       #118
      010013FCH   LINE      CODE     ---       #119
      010013FDH   LINE      CODE     ---       #120
      010013FEH   LINE      CODE     ---       #121
      010013FFH   LINE      CODE     ---       #122
      01001400H   LINE      CODE     ---       #123
      01001405H   LINE      CODE     ---       #124
      01001405H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      01001406H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001406H   LINE      CODE     ---       #137
      01001408H   LINE      CODE     ---       #138
      01001408H   LINE      CODE     ---       #139
      0100140AH   LINE      CODE     ---       #140
      0100140DH   LINE      CODE     ---       #141
      01001410H   LINE      CODE     ---       #142
      01001410H   LINE      CODE     ---       #143
      01001412H   LINE      CODE     ---       #144
      01001417H   LINE      CODE     ---       #145
      01001418H   LINE      CODE     ---       #146
      01001419H   LINE      CODE     ---       #147
      0100141AH   LINE      CODE     ---       #148
      0100141BH   LINE      CODE     ---       #149
      0100141CH   LINE      CODE     ---       #150
      0100141DH   LINE      CODE     ---       #151
      01001422H   LINE      CODE     ---       #152
      01001424H   LINE      CODE     ---       #153
      01001426H   LINE      CODE     ---       #155
      01001426H   LINE      CODE     ---       #156
      0100142BH   LINE      CODE     ---       #157
      0100142CH   LINE      CODE     ---       #158
      0100142DH   LINE      CODE     ---       #159
      0100142EH   LINE      CODE     ---       #160
      0100142FH   LINE      CODE     ---       #161
      01001430H   LINE      CODE     ---       #162
      01001431H   LINE      CODE     ---       #163
      01001436H   LINE      CODE     ---       #164
      01001436H   LINE      CODE     ---       #165
      01001438H   LINE      CODE     ---       #166
      ---         BLOCKEND  ---      ---       LVL=0

      01001439H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001439H   LINE      CODE     ---       #177
      0100143BH   LINE      CODE     ---       #178
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 38


      0100143BH   LINE      CODE     ---       #179
      0100143DH   LINE      CODE     ---       #180
      01001440H   LINE      CODE     ---       #181
      01001443H   LINE      CODE     ---       #182
      01001443H   LINE      CODE     ---       #183
      01001445H   LINE      CODE     ---       #184
      0100144AH   LINE      CODE     ---       #185
      0100144BH   LINE      CODE     ---       #186
      0100144CH   LINE      CODE     ---       #187
      0100144DH   LINE      CODE     ---       #188
      0100144EH   LINE      CODE     ---       #189
      0100144FH   LINE      CODE     ---       #190
      01001450H   LINE      CODE     ---       #191
      01001455H   LINE      CODE     ---       #192
      01001457H   LINE      CODE     ---       #193
      01001458H   LINE      CODE     ---       #195
      01001458H   LINE      CODE     ---       #196
      0100145DH   LINE      CODE     ---       #197
      0100145EH   LINE      CODE     ---       #198
      0100145FH   LINE      CODE     ---       #199
      01001460H   LINE      CODE     ---       #200
      01001461H   LINE      CODE     ---       #201
      01001462H   LINE      CODE     ---       #202
      01001463H   LINE      CODE     ---       #203
      01001468H   LINE      CODE     ---       #204
      01001468H   LINE      CODE     ---       #205
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC
      0100150CH   PUBLIC    CODE     ---       ADC_GetADCResult
      01001638H   PUBLIC    CODE     ---       _ADC_ConfigAN31
      010015D3H   PUBLIC    CODE     ---       _ADC_EnableChannel
      0100160EH   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      0100001EH   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 39


      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 40


      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 41


      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #66
      0100001EH   LINE      CODE     ---       #67
      0100001EH   LINE      CODE     ---       #68
      01000021H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      0100160EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      0100160EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100160EH   LINE      CODE     ---       #88
      0100160EH   LINE      CODE     ---       #89
      0100160EH   LINE      CODE     ---       #90
      0100160EH   LINE      CODE     ---       #92
      01001610H   LINE      CODE     ---       #93
      01001613H   LINE      CODE     ---       #94
      01001614H   LINE      CODE     ---       #95
      01001616H   LINE      CODE     ---       #97
      01001618H   LINE      CODE     ---       #98
      0100161CH   LINE      CODE     ---       #99
      01001621H   LINE      CODE     ---       #100
      01001623H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      010015D3H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      010015D3H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010015D3H   LINE      CODE     ---       #154
      010015D3H   LINE      CODE     ---       #155
      010015D3H   LINE      CODE     ---       #156
      010015D3H   LINE      CODE     ---       #158
      010015D5H   LINE      CODE     ---       #159
      010015D9H   LINE      CODE     ---       #160
      010015E2H   LINE      CODE     ---       #161
      010015E4H   LINE      CODE     ---       #163
      010015E6H   LINE      CODE     ---       #164
      010015EAH   LINE      CODE     ---       #165
      010015EEH   LINE      CODE     ---       #166
      010015F0H   LINE      CODE     ---       #168
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 42


      ---         BLOCKEND  ---      ---       LVL=0

      01001638H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      01001638H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001638H   LINE      CODE     ---       #177
      01001638H   LINE      CODE     ---       #178
      01001638H   LINE      CODE     ---       #179
      01001638H   LINE      CODE     ---       #181
      0100163AH   LINE      CODE     ---       #182
      0100163EH   LINE      CODE     ---       #183
      01001644H   LINE      CODE     ---       #184
      01001646H   LINE      CODE     ---       #186
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      0100150CH   BLOCK     CODE     ---       LVL=0
      0100150CH   LINE      CODE     ---       #258
      0100150CH   LINE      CODE     ---       #259
      0100150CH   LINE      CODE     ---       #260
      01001513H   LINE      CODE     ---       #261
      01001513H   LINE      CODE     ---       #262
      01001526H   LINE      CODE     ---       #263
      01001526H   LINE      CODE     ---       #264
      01001530H   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       ISR
      02000024H   PUBLIC    XDATA    BYTE      ledonoff_cnt
      02000023H   PUBLIC    XDATA    BYTE      K3_release_cnt
      02000022H   PUBLIC    XDATA    BYTE      K2_release_cnt
      02000021H   PUBLIC    XDATA    BYTE      K1_release_cnt
      0200001FH   PUBLIC    XDATA    INT       tmr4_cnt1
      0200001DH   PUBLIC    XDATA    INT       tmr4_cnt
      0200001CH   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      0200001BH   PUBLIC    XDATA    BYTE      can_RF_change_status_cnt
      0200001AH   PUBLIC    XDATA    BYTE      canADC_cnt
      02000018H   PUBLIC    XDATA    WORD      speedup_cnt
      02000016H   PUBLIC    XDATA    WORD      interval_cnt
      01000037H   PUBLIC    CODE     ---       SPI_IRQHandler
      01000036H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000032H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000031H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000030H   PUBLIC    CODE     ---       EPWM_IRQHandler
      0100116FH   PUBLIC    CODE     ---       Timer4_IRQHandler
      0100002FH   PUBLIC    CODE     ---       Timer3_IRQHandler
      0100002EH   PUBLIC    CODE     ---       ACMP_IRQHandler
      0100002AH   PUBLIC    CODE     ---       LSE_IRQHandler
      01000029H   PUBLIC    CODE     ---       LVD_IRQHandler
      010011F5H   PUBLIC    CODE     ---       P3EI_IRQHandler
      01000028H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01000027H   PUBLIC    CODE     ---       P1EI_IRQHandler
      01001389H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000026H   PUBLIC    CODE     ---       UART1_IRQHandler
      01000022H   PUBLIC    CODE     ---       Timer2_IRQHandler
      0100001AH   PUBLIC    CODE     ---       UART0_IRQHandler
      01000E06H   PUBLIC    CODE     ---       Timer1_IRQHandler
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 43


      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      01000086H   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 44


      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 45


      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #92
      0100000AH   LINE      CODE     ---       #95
      ---         BLOCKEND  ---      ---       LVL=0

      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #103
      01000086H   LINE      CODE     ---       #105
      01000089H   LINE      CODE     ---       #106
      0100008CH   LINE      CODE     ---       #185
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 46


      01000012H   LINE      CODE     ---       #193
      01000012H   LINE      CODE     ---       #196
      ---         BLOCKEND  ---      ---       LVL=0

      01000E06H   BLOCK     CODE     ---       LVL=0
      01000E06H   LINE      CODE     ---       #204
      01000E13H   LINE      CODE     ---       #206
      01000E16H   LINE      CODE     ---       #207
      01000E19H   LINE      CODE     ---       #209
      01000E1FH   LINE      CODE     ---       #210
      01000E1FH   LINE      CODE     ---       #211
      01000E25H   LINE      CODE     ---       #212
      01000E2FH   LINE      CODE     ---       #213
      01000E2FH   LINE      CODE     ---       #214
      01000E34H   LINE      CODE     ---       #215
      01000E36H   LINE      CODE     ---       #216
      01000E36H   LINE      CODE     ---       #217
      01000E36H   LINE      CODE     ---       #219
      01000E40H   LINE      CODE     ---       #220
      01000E40H   LINE      CODE     ---       #221
      01000E4EH   LINE      CODE     ---       #222
      01000E5DH   LINE      CODE     ---       #223
      01000E5DH   LINE      CODE     ---       #224
      01000E5FH   LINE      CODE     ---       #225
      01000E5FH   LINE      CODE     ---       #226
      01000E61H   LINE      CODE     ---       #228
      01000E61H   LINE      CODE     ---       #229
      01000E6AH   LINE      CODE     ---       #230
      01000E6AH   LINE      CODE     ---       #232
      01000E6DH   LINE      CODE     ---       #233
      01000E6DH   LINE      CODE     ---       #234
      01000E7BH   LINE      CODE     ---       #235
      01000E8EH   LINE      CODE     ---       #236
      01000E8EH   LINE      CODE     ---       #237
      01000E90H   LINE      CODE     ---       #238
      01000E90H   LINE      CODE     ---       #239
      01000E90H   LINE      CODE     ---       #240
      01000E92H   LINE      CODE     ---       #242
      01000E92H   LINE      CODE     ---       #243
      01000E99H   LINE      CODE     ---       #244
      01000E99H   LINE      CODE     ---       #280
      01000E9CH   LINE      CODE     ---       #281
      01000E9CH   LINE      CODE     ---       #282
      01000EA5H   LINE      CODE     ---       #283
      01000EA7H   LINE      CODE     ---       #285
      01000EA7H   LINE      CODE     ---       #286
      01000EACH   LINE      CODE     ---       #287
      01000EACH   LINE      CODE     ---       #289
      01000EAFH   LINE      CODE     ---       #290
      01000EAFH   LINE      CODE     ---       #291
      01000EB8H   LINE      CODE     ---       #292
      01000EBAH   LINE      CODE     ---       #294
      01000EBAH   LINE      CODE     ---       #295
      01000EBFH   LINE      CODE     ---       #296
      01000EBFH   LINE      CODE     ---       #298
      01000EC2H   LINE      CODE     ---       #299
      01000EC2H   LINE      CODE     ---       #300
      01000ECBH   LINE      CODE     ---       #301
      01000ECDH   LINE      CODE     ---       #303
      01000ECDH   LINE      CODE     ---       #304
      01000ED2H   LINE      CODE     ---       #305
      01000ED2H   LINE      CODE     ---       #308
      01000ED5H   LINE      CODE     ---       #309
      01000ED5H   LINE      CODE     ---       #310
      01000ED8H   LINE      CODE     ---       #311
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 47


      01000ED8H   LINE      CODE     ---       #312
      01000EDEH   LINE      CODE     ---       #313
      01000EE8H   LINE      CODE     ---       #314
      01000EE8H   LINE      CODE     ---       #315
      01000EEAH   LINE      CODE     ---       #316
      01000EECH   LINE      CODE     ---       #317
      01000EECH   LINE      CODE     ---       #318
      01000EEEH   LINE      CODE     ---       #320
      01000EEEH   LINE      CODE     ---       #321
      01000EF3H   LINE      CODE     ---       #322
      01000EF3H   LINE      CODE     ---       #323
      01000EF3H   LINE      CODE     ---       #325
      01000EF6H   LINE      CODE     ---       #326
      01000EF6H   LINE      CODE     ---       #327
      01000EF9H   LINE      CODE     ---       #328
      01000EF9H   LINE      CODE     ---       #329
      01000EFFH   LINE      CODE     ---       #330
      01000F09H   LINE      CODE     ---       #331
      01000F09H   LINE      CODE     ---       #332
      01000F0BH   LINE      CODE     ---       #333
      01000F0DH   LINE      CODE     ---       #334
      01000F0DH   LINE      CODE     ---       #335
      01000F0FH   LINE      CODE     ---       #337
      01000F0FH   LINE      CODE     ---       #338
      01000F14H   LINE      CODE     ---       #339
      01000F14H   LINE      CODE     ---       #340
      01000F14H   LINE      CODE     ---       #342
      01000F17H   LINE      CODE     ---       #343
      01000F17H   LINE      CODE     ---       #344
      01000F1AH   LINE      CODE     ---       #345
      01000F1AH   LINE      CODE     ---       #346
      01000F20H   LINE      CODE     ---       #347
      01000F2AH   LINE      CODE     ---       #348
      01000F2AH   LINE      CODE     ---       #349
      01000F2CH   LINE      CODE     ---       #350
      01000F2EH   LINE      CODE     ---       #351
      01000F2EH   LINE      CODE     ---       #352
      01000F30H   LINE      CODE     ---       #354
      01000F30H   LINE      CODE     ---       #355
      01000F35H   LINE      CODE     ---       #356
      01000F35H   LINE      CODE     ---       #357
      01000F35H   LINE      CODE     ---       #360
      01000F3BH   LINE      CODE     ---       #361
      01000F45H   LINE      CODE     ---       #362
      01000F45H   LINE      CODE     ---       #363
      01000F47H   LINE      CODE     ---       #364
      01000F49H   LINE      CODE     ---       #365
      01000F49H   LINE      CODE     ---       #368
      01000F4FH   LINE      CODE     ---       #369
      01000F59H   LINE      CODE     ---       #370
      01000F59H   LINE      CODE     ---       #371
      01000F5BH   LINE      CODE     ---       #372
      01000F5DH   LINE      CODE     ---       #373
      01000F5DH   LINE      CODE     ---       #374
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #382
      0100001AH   LINE      CODE     ---       #385
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #393
      01000022H   LINE      CODE     ---       #396
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 48



      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #404
      01000026H   LINE      CODE     ---       #407
      ---         BLOCKEND  ---      ---       LVL=0

      01001389H   BLOCK     CODE     ---       LVL=0
      01001389H   LINE      CODE     ---       #415
      010013A6H   LINE      CODE     ---       #417
      010013B0H   LINE      CODE     ---       #418
      010013B0H   LINE      CODE     ---       #419
      010013B7H   LINE      CODE     ---       #420
      010013B7H   LINE      CODE     ---       #421
      ---         BLOCKEND  ---      ---       LVL=0

      01000027H   BLOCK     CODE     ---       LVL=0
      01000027H   LINE      CODE     ---       #429
      01000027H   LINE      CODE     ---       #431
      ---         BLOCKEND  ---      ---       LVL=0

      01000028H   BLOCK     CODE     ---       LVL=0
      01000028H   LINE      CODE     ---       #439
      01000028H   LINE      CODE     ---       #442
      ---         BLOCKEND  ---      ---       LVL=0

      010011F5H   BLOCK     CODE     ---       LVL=0
      010011F5H   LINE      CODE     ---       #450
      01001212H   LINE      CODE     ---       #452
      0100121CH   LINE      CODE     ---       #453
      0100121CH   LINE      CODE     ---       #454
      01001223H   LINE      CODE     ---       #455
      01001223H   LINE      CODE     ---       #456
      0100122DH   LINE      CODE     ---       #457
      0100122DH   LINE      CODE     ---       #458
      01001234H   LINE      CODE     ---       #459
      01001234H   LINE      CODE     ---       #460
      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #469
      01000029H   LINE      CODE     ---       #472
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #480
      0100002AH   LINE      CODE     ---       #483
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #492
      0100002EH   LINE      CODE     ---       #495
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #503
      0100002FH   LINE      CODE     ---       #506
      ---         BLOCKEND  ---      ---       LVL=0

      0100116FH   BLOCK     CODE     ---       LVL=0
      0100116FH   LINE      CODE     ---       #514
      01001180H   LINE      CODE     ---       #516
      01001183H   LINE      CODE     ---       #517
      01001186H   LINE      CODE     ---       #519
      0100118CH   LINE      CODE     ---       #520
      0100118CH   LINE      CODE     ---       #521
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 49


      0100119AH   LINE      CODE     ---       #522
      010011B5H   LINE      CODE     ---       #523
      010011B5H   LINE      CODE     ---       #524
      010011B9H   LINE      CODE     ---       #525
      010011C7H   LINE      CODE     ---       #526
      010011D7H   LINE      CODE     ---       #527
      010011D7H   LINE      CODE     ---       #528
      010011D9H   LINE      CODE     ---       #530
      010011D9H   LINE      CODE     ---       #531
      010011E0H   LINE      CODE     ---       #532
      010011E6H   LINE      CODE     ---       #533
      010011E6H   LINE      CODE     ---       #534
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #542
      01000030H   LINE      CODE     ---       #545
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #553
      01000031H   LINE      CODE     ---       #556
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #564
      01000032H   LINE      CODE     ---       #567
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #575
      01000036H   LINE      CODE     ---       #578
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #586
      01000037H   LINE      CODE     ---       #589
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000021H.6 PUBLIC    BIT      BIT       canADC
      00000021H.5 PUBLIC    BIT      BIT       speedup
      00000021H.4 PUBLIC    BIT      BIT       longhit
      02000012H   PUBLIC    XDATA    FLOAT     BatV
      02000011H   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000021H.3 PUBLIC    BIT      BIT       ledonoff
      00000021H.2 PUBLIC    BIT      BIT       isPoweron
      02000010H   PUBLIC    XDATA    BYTE      dly
      0200000FH   PUBLIC    XDATA    BYTE      direction
      00000021H.1 PUBLIC    BIT      BIT       is_running
      00000021H.0 PUBLIC    BIT      BIT       batlow1
      0200000EH   PUBLIC    XDATA    BYTE      motor_status
      0200000DH   PUBLIC    XDATA    BYTE      batlow_cnt
      0200000CH   PUBLIC    XDATA    BYTE      K3_num
      0200000BH   PUBLIC    XDATA    BYTE      K1_num
      00000020H.7 PUBLIC    BIT      BIT       mode
      0200000AH   PUBLIC    XDATA    BYTE      K3_cnt
      02000009H   PUBLIC    XDATA    BYTE      K2_cnt
      02000007H   PUBLIC    XDATA    WORD      result
      02000006H   PUBLIC    XDATA    BYTE      K1_cnt
      02000004H   PUBLIC    XDATA    WORD      longhit_cnt
      02000000H   PUBLIC    XDATA    DWORD     Systemclock
      00000020H.6 PUBLIC    BIT      BIT       K3_cnt_EN
      00000020H.5 PUBLIC    BIT      BIT       K2_cnt_EN
      00000020H.4 PUBLIC    BIT      BIT       K1_cnt_EN
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 50


      00000020H.3 PUBLIC    BIT      BIT       batlow
      00000020H.2 PUBLIC    BIT      BIT       keydown
      00000020H.1 PUBLIC    BIT      BIT       ledonoff1
      00000020H.0 PUBLIC    BIT      BIT       can_RF_change_status
      01001033H   PUBLIC    CODE     ---       Get_BatV
      01001535H   PUBLIC    CODE     ---       _delay1ms
      0100086CH   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 51


      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 52


      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100086CH   BLOCK     CODE     ---       LVL=0
      0100086CH   BLOCK     CODE     NEAR LAB  LVL=1
      01000879H   SYMBOL    CODE     VOID      sleep
      ---         BLOCKEND  ---      ---       LVL=1
      0100086CH   LINE      CODE     ---       #98
      0100086CH   LINE      CODE     ---       #99
      0100086CH   LINE      CODE     ---       #102
      0100086FH   LINE      CODE     ---       #103
      01000872H   LINE      CODE     ---       #104
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 53


      01000875H   LINE      CODE     ---       #105
      01000878H   LINE      CODE     ---       #106
      0100087BH   LINE      CODE     ---       #108
      0100087BH   LINE      CODE     ---       #109
      0100087BH   LINE      CODE     ---       #110
      0100087BH   LINE      CODE     ---       #111
      0100087BH   LINE      CODE     ---       #113
      0100087EH   LINE      CODE     ---       #116
      01000881H   LINE      CODE     ---       #120
      01000884H   LINE      CODE     ---       #128
      01000887H   LINE      CODE     ---       #129
      0100088AH   LINE      CODE     ---       #130
      01000897H   LINE      CODE     ---       #131
      0100089AH   LINE      CODE     ---       #132
      0100089DH   LINE      CODE     ---       #133
      010008A4H   LINE      CODE     ---       #134
      010008A4H   LINE      CODE     ---       #135
      010008A7H   LINE      CODE     ---       #136
      010008A7H   LINE      CODE     ---       #138
      010008A7H   LINE      CODE     ---       #139
      010008A7H   LINE      CODE     ---       #140
      010008CBH   LINE      CODE     ---       #141
      010008CBH   LINE      CODE     ---       #142
      010008CBH   LINE      CODE     ---       #149
      010008CEH   LINE      CODE     ---       #150
      010008D1H   LINE      CODE     ---       #152
      010008D9H   LINE      CODE     ---       #154
      010008E6H   LINE      CODE     ---       #155
      010008E9H   LINE      CODE     ---       #156
      010008ECH   LINE      CODE     ---       #158
      010008EEH   LINE      CODE     ---       #159
      010008F0H   LINE      CODE     ---       #160
      010008F9H   LINE      CODE     ---       #163
      010008FDH   LINE      CODE     ---       #164
      01000901H   LINE      CODE     ---       #165
      01000909H   LINE      CODE     ---       #168
      0100091AH   LINE      CODE     ---       #170
      01000921H   LINE      CODE     ---       #171
      01000928H   LINE      CODE     ---       #172
      0100092FH   LINE      CODE     ---       #173
      01000932H   LINE      CODE     ---       #174
      01000935H   LINE      CODE     ---       #177
      0100093CH   LINE      CODE     ---       #178
      01000943H   LINE      CODE     ---       #179
      0100094AH   LINE      CODE     ---       #180
      0100094AH   LINE      CODE     ---       #181
      0100094AH   LINE      CODE     ---       #182
      0100094AH   LINE      CODE     ---       #183
      0100094AH   LINE      CODE     ---       #185
      01000956H   LINE      CODE     ---       #186
      01000956H   LINE      CODE     ---       #187
      01000959H   LINE      CODE     ---       #191
      0100095CH   LINE      CODE     ---       #192
      0100095FH   LINE      CODE     ---       #193
      0100096CH   LINE      CODE     ---       #194
      0100096FH   LINE      CODE     ---       #195
      01000972H   LINE      CODE     ---       #196
      01000979H   LINE      CODE     ---       #197
      01000979H   LINE      CODE     ---       #198
      0100097CH   LINE      CODE     ---       #199
      0100097CH   LINE      CODE     ---       #201
      0100097CH   LINE      CODE     ---       #202
      0100097CH   LINE      CODE     ---       #203
      0100097CH   LINE      CODE     ---       #205
      01000982H   LINE      CODE     ---       #206
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 54


      01000982H   LINE      CODE     ---       #207
      01000988H   LINE      CODE     ---       #208
      01000988H   LINE      CODE     ---       #209
      0100098FH   LINE      CODE     ---       #210
      01000995H   LINE      CODE     ---       #211
      01000995H   LINE      CODE     ---       #212
      01000995H   LINE      CODE     ---       #213
      01000997H   LINE      CODE     ---       #214
      0100099AH   LINE      CODE     ---       #215
      0100099AH   LINE      CODE     ---       #216
      0100099DH   LINE      CODE     ---       #217
      0100099DH   LINE      CODE     ---       #218
      0100099FH   LINE      CODE     ---       #219
      010009A1H   LINE      CODE     ---       #221
      010009A1H   LINE      CODE     ---       #222
      010009A3H   LINE      CODE     ---       #223
      010009A3H   LINE      CODE     ---       #224
      010009A5H   LINE      CODE     ---       #226
      010009A5H   LINE      CODE     ---       #227
      010009A7H   LINE      CODE     ---       #228
      010009A7H   LINE      CODE     ---       #230
      010009ADH   LINE      CODE     ---       #231
      010009ADH   LINE      CODE     ---       #232
      010009ADH   LINE      CODE     ---       #233
      010009ADH   LINE      CODE     ---       #236
      010009BAH   LINE      CODE     ---       #237
      010009BAH   LINE      CODE     ---       #238
      010009BEH   LINE      CODE     ---       #239
      010009C0H   LINE      CODE     ---       #240
      010009C3H   LINE      CODE     ---       #241
      010009C3H   LINE      CODE     ---       #242
      010009CBH   LINE      CODE     ---       #243
      010009CBH   LINE      CODE     ---       #244
      010009CEH   LINE      CODE     ---       #245
      010009D1H   LINE      CODE     ---       #246
      010009D4H   LINE      CODE     ---       #247
      010009D9H   LINE      CODE     ---       #248
      010009DBH   LINE      CODE     ---       #249
      010009DBH   LINE      CODE     ---       #251
      010009DEH   LINE      CODE     ---       #252
      010009DEH   LINE      CODE     ---       #253
      010009E3H   LINE      CODE     ---       #254
      010009E3H   LINE      CODE     ---       #255
      010009E5H   LINE      CODE     ---       #256
      010009E5H   LINE      CODE     ---       #259
      010009E8H   LINE      CODE     ---       #260
      010009E8H   LINE      CODE     ---       #261
      010009F5H   LINE      CODE     ---       #262
      010009F5H   LINE      CODE     ---       #263
      010009F9H   LINE      CODE     ---       #264
      010009FBH   LINE      CODE     ---       #265
      010009FEH   LINE      CODE     ---       #266
      010009FEH   LINE      CODE     ---       #267
      01000A06H   LINE      CODE     ---       #268
      01000A06H   LINE      CODE     ---       #269
      01000A09H   LINE      CODE     ---       #270
      01000A0EH   LINE      CODE     ---       #271
      01000A0EH   LINE      CODE     ---       #272
      01000A0EH   LINE      CODE     ---       #273
      01000A0EH   LINE      CODE     ---       #275
      01000A14H   LINE      CODE     ---       #276
      01000A14H   LINE      CODE     ---       #278
      01000A1AH   LINE      CODE     ---       #279
      01000A1AH   LINE      CODE     ---       #280
      01000A1DH   LINE      CODE     ---       #281
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 55


      01000A1DH   LINE      CODE     ---       #282
      01000A1FH   LINE      CODE     ---       #283
      01000A25H   LINE      CODE     ---       #284
      01000A25H   LINE      CODE     ---       #285
      01000A27H   LINE      CODE     ---       #286
      01000A29H   LINE      CODE     ---       #288
      01000A29H   LINE      CODE     ---       #289
      01000A2CH   LINE      CODE     ---       #290
      01000A2CH   LINE      CODE     ---       #291
      01000A2EH   LINE      CODE     ---       #292
      01000A30H   LINE      CODE     ---       #294
      01000A30H   LINE      CODE     ---       #295
      01000A32H   LINE      CODE     ---       #296
      01000A32H   LINE      CODE     ---       #297
      01000A32H   LINE      CODE     ---       #298
      01000A34H   LINE      CODE     ---       #300
      01000A34H   LINE      CODE     ---       #301
      01000A37H   LINE      CODE     ---       #302
      01000A37H   LINE      CODE     ---       #303
      01000A39H   LINE      CODE     ---       #304
      01000A3FH   LINE      CODE     ---       #305
      01000A3FH   LINE      CODE     ---       #306
      01000A41H   LINE      CODE     ---       #307
      01000A43H   LINE      CODE     ---       #309
      01000A43H   LINE      CODE     ---       #310
      01000A46H   LINE      CODE     ---       #311
      01000A46H   LINE      CODE     ---       #312
      01000A48H   LINE      CODE     ---       #313
      01000A4AH   LINE      CODE     ---       #315
      01000A4AH   LINE      CODE     ---       #316
      01000A4CH   LINE      CODE     ---       #317
      01000A4CH   LINE      CODE     ---       #318
      01000A4CH   LINE      CODE     ---       #319
      01000A4EH   LINE      CODE     ---       #321
      01000A4EH   LINE      CODE     ---       #322
      01000A50H   LINE      CODE     ---       #323
      01000A56H   LINE      CODE     ---       #324
      01000A56H   LINE      CODE     ---       #325
      01000A58H   LINE      CODE     ---       #326
      01000A5AH   LINE      CODE     ---       #328
      01000A5AH   LINE      CODE     ---       #329
      01000A5DH   LINE      CODE     ---       #330
      01000A5DH   LINE      CODE     ---       #331
      01000A5FH   LINE      CODE     ---       #332
      01000A61H   LINE      CODE     ---       #334
      01000A61H   LINE      CODE     ---       #335
      01000A63H   LINE      CODE     ---       #336
      01000A63H   LINE      CODE     ---       #337
      01000A63H   LINE      CODE     ---       #338
      01000A63H   LINE      CODE     ---       #339
      01000A63H   LINE      CODE     ---       #342
      01000A69H   LINE      CODE     ---       #343
      01000A69H   LINE      CODE     ---       #344
      01000A69H   LINE      CODE     ---       #345
      01000A69H   LINE      CODE     ---       #346
      01000A69H   LINE      CODE     ---       #348
      01000A6FH   LINE      CODE     ---       #349
      01000A6FH   LINE      CODE     ---       #350
      01000A76H   LINE      CODE     ---       #351
      01000A7CH   LINE      CODE     ---       #352
      01000A7EH   LINE      CODE     ---       #353
      01000A81H   LINE      CODE     ---       #354
      01000A81H   LINE      CODE     ---       #355
      01000A84H   LINE      CODE     ---       #356
      01000A84H   LINE      CODE     ---       #357
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 56


      01000A86H   LINE      CODE     ---       #358
      01000A88H   LINE      CODE     ---       #360
      01000A88H   LINE      CODE     ---       #361
      01000A8AH   LINE      CODE     ---       #362
      01000A8AH   LINE      CODE     ---       #363
      01000A8CH   LINE      CODE     ---       #365
      01000A8CH   LINE      CODE     ---       #366
      01000A8EH   LINE      CODE     ---       #367
      01000A8EH   LINE      CODE     ---       #368
      01000A8EH   LINE      CODE     ---       #506
      01000AA6H   LINE      CODE     ---       #507
      01000AA6H   LINE      CODE     ---       #509
      01000AA6H   LINE      CODE     ---       #510
      01000AABH   LINE      CODE     ---       #511
      01000AADH   LINE      CODE     ---       #512
      01000AADH   LINE      CODE     ---       #513
      01000AADH   LINE      CODE     ---       #516
      01000ADBH   LINE      CODE     ---       #517
      01000ADBH   LINE      CODE     ---       #518
      01000ADBH   LINE      CODE     ---       #519
      01000ADBH   LINE      CODE     ---       #520
      01000ADDH   LINE      CODE     ---       #521
      01000AE0H   LINE      CODE     ---       #522
      01000AE0H   LINE      CODE     ---       #523
      01000AE0H   LINE      CODE     ---       #524
      01000AE0H   LINE      CODE     ---       #525
      01000AE8H   LINE      CODE     ---       #526
      01000AE8H   LINE      CODE     ---       #527
      01000AEAH   LINE      CODE     ---       #528
      01000AEAH   LINE      CODE     ---       #529
      01000AF0H   LINE      CODE     ---       #531
      01000AF7H   LINE      CODE     ---       #532
      01000AF7H   LINE      CODE     ---       #533
      01000AFBH   LINE      CODE     ---       #534
      01000AFDH   LINE      CODE     ---       #536
      01000AFDH   LINE      CODE     ---       #537
      01000B02H   LINE      CODE     ---       #538
      01000B02H   LINE      CODE     ---       #540
      01000B21H   LINE      CODE     ---       #541
      01000B21H   LINE      CODE     ---       #542
      01000B21H   LINE      CODE     ---       #543
      01000B21H   LINE      CODE     ---       #544
      01000B23H   LINE      CODE     ---       #545
      01000B26H   LINE      CODE     ---       #546
      01000B26H   LINE      CODE     ---       #547
      01000B26H   LINE      CODE     ---       #548
      01000B26H   LINE      CODE     ---       #549
      01000B26H   LINE      CODE     ---       #550
      01000B29H   LINE      CODE     ---       #551
      01000B29H   LINE      CODE     ---       #552
      01000B29H   LINE      CODE     ---       #553
      01000B29H   LINE      CODE     ---       #554
      01000B29H   LINE      CODE     ---       #555
      01000B2CH   LINE      CODE     ---       #556
      01000B2CH   LINE      CODE     ---       #557
      01000B2CH   LINE      CODE     ---       #558
      01000B2CH   LINE      CODE     ---       #559
      01000B2CH   LINE      CODE     ---       #560
      01000B2FH   LINE      CODE     ---       #561
      01000B2FH   LINE      CODE     ---       #562
      01000B2FH   LINE      CODE     ---       #563
      01000B2FH   LINE      CODE     ---       #564
      01000B2FH   LINE      CODE     ---       #565
      01000B2FH   LINE      CODE     ---       #566
      01000B2FH   LINE      CODE     ---       #567
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 57


      01000B2FH   LINE      CODE     ---       #568
      01000B2FH   LINE      CODE     ---       #569
      01000B2FH   LINE      CODE     ---       #570
      01000B2FH   LINE      CODE     ---       #571
      01000B37H   LINE      CODE     ---       #572
      01000B37H   LINE      CODE     ---       #573
      01000B39H   LINE      CODE     ---       #574
      01000B39H   LINE      CODE     ---       #575
      01000B3FH   LINE      CODE     ---       #577
      01000B46H   LINE      CODE     ---       #578
      01000B46H   LINE      CODE     ---       #579
      01000B4AH   LINE      CODE     ---       #580
      01000B4CH   LINE      CODE     ---       #582
      01000B4CH   LINE      CODE     ---       #583
      01000B51H   LINE      CODE     ---       #584
      01000B51H   LINE      CODE     ---       #586
      01000B70H   LINE      CODE     ---       #587
      01000B70H   LINE      CODE     ---       #588
      01000B70H   LINE      CODE     ---       #589
      01000B70H   LINE      CODE     ---       #590
      01000B70H   LINE      CODE     ---       #591
      01000B73H   LINE      CODE     ---       #592
      01000B73H   LINE      CODE     ---       #593
      01000B73H   LINE      CODE     ---       #594
      01000B73H   LINE      CODE     ---       #595
      01000B73H   LINE      CODE     ---       #596
      01000B76H   LINE      CODE     ---       #597
      01000B76H   LINE      CODE     ---       #598
      01000B76H   LINE      CODE     ---       #599
      01000B76H   LINE      CODE     ---       #600
      01000B76H   LINE      CODE     ---       #601
      01000B79H   LINE      CODE     ---       #602
      01000B79H   LINE      CODE     ---       #603
      01000B79H   LINE      CODE     ---       #604
      01000B79H   LINE      CODE     ---       #605
      01000B7BH   LINE      CODE     ---       #606
      01000B7EH   LINE      CODE     ---       #607
      01000B7EH   LINE      CODE     ---       #608
      01000B7EH   LINE      CODE     ---       #609
      01000B7EH   LINE      CODE     ---       #610
      01000B7EH   LINE      CODE     ---       #611
      01000B7EH   LINE      CODE     ---       #612
      01000B7EH   LINE      CODE     ---       #613
      01000B7EH   LINE      CODE     ---       #614
      01000B7EH   LINE      CODE     ---       #615
      01000B7EH   LINE      CODE     ---       #616
      01000B7EH   LINE      CODE     ---       #617
      01000B86H   LINE      CODE     ---       #618
      01000B86H   LINE      CODE     ---       #619
      01000B88H   LINE      CODE     ---       #620
      01000B88H   LINE      CODE     ---       #621
      01000B8EH   LINE      CODE     ---       #623
      01000B95H   LINE      CODE     ---       #624
      01000B95H   LINE      CODE     ---       #625
      01000B99H   LINE      CODE     ---       #626
      01000B9BH   LINE      CODE     ---       #628
      01000B9BH   LINE      CODE     ---       #629
      01000BA0H   LINE      CODE     ---       #630
      01000BA0H   LINE      CODE     ---       #632
      01000BBFH   LINE      CODE     ---       #633
      01000BBFH   LINE      CODE     ---       #634
      01000BBFH   LINE      CODE     ---       #635
      01000BBFH   LINE      CODE     ---       #636
      01000BC1H   LINE      CODE     ---       #637
      01000BC4H   LINE      CODE     ---       #638
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 58


      01000BC4H   LINE      CODE     ---       #639
      01000BC4H   LINE      CODE     ---       #640
      01000BC4H   LINE      CODE     ---       #641
      01000BC4H   LINE      CODE     ---       #642
      01000BC7H   LINE      CODE     ---       #643
      01000BC7H   LINE      CODE     ---       #644
      01000BC7H   LINE      CODE     ---       #645
      01000BC7H   LINE      CODE     ---       #646
      01000BC7H   LINE      CODE     ---       #647
      01000BCAH   LINE      CODE     ---       #648
      01000BCAH   LINE      CODE     ---       #649
      01000BCAH   LINE      CODE     ---       #650
      01000BCAH   LINE      CODE     ---       #651
      01000BCAH   LINE      CODE     ---       #652
      01000BCDH   LINE      CODE     ---       #653
      01000BCDH   LINE      CODE     ---       #654
      01000BCDH   LINE      CODE     ---       #655
      01000BCDH   LINE      CODE     ---       #656
      01000BCDH   LINE      CODE     ---       #657
      01000BCDH   LINE      CODE     ---       #658
      01000BCDH   LINE      CODE     ---       #659
      01000BCDH   LINE      CODE     ---       #660
      01000BCDH   LINE      CODE     ---       #661
      01000BCDH   LINE      CODE     ---       #662
      01000BCDH   LINE      CODE     ---       #663
      01000BD5H   LINE      CODE     ---       #664
      01000BD5H   LINE      CODE     ---       #665
      01000BD7H   LINE      CODE     ---       #666
      01000BD7H   LINE      CODE     ---       #667
      01000BDDH   LINE      CODE     ---       #669
      01000BE4H   LINE      CODE     ---       #670
      01000BE4H   LINE      CODE     ---       #671
      01000BE8H   LINE      CODE     ---       #672
      01000BEAH   LINE      CODE     ---       #674
      01000BEAH   LINE      CODE     ---       #675
      01000BEFH   LINE      CODE     ---       #676
      01000BEFH   LINE      CODE     ---       #678
      01000C0EH   LINE      CODE     ---       #679
      01000C0EH   LINE      CODE     ---       #680
      01000C0EH   LINE      CODE     ---       #681
      01000C0EH   LINE      CODE     ---       #682
      01000C0EH   LINE      CODE     ---       #683
      01000C11H   LINE      CODE     ---       #684
      01000C11H   LINE      CODE     ---       #685
      01000C11H   LINE      CODE     ---       #686
      01000C11H   LINE      CODE     ---       #687
      01000C11H   LINE      CODE     ---       #688
      01000C14H   LINE      CODE     ---       #689
      01000C14H   LINE      CODE     ---       #690
      01000C14H   LINE      CODE     ---       #691
      01000C14H   LINE      CODE     ---       #692
      01000C14H   LINE      CODE     ---       #693
      01000C17H   LINE      CODE     ---       #694
      01000C17H   LINE      CODE     ---       #695
      01000C17H   LINE      CODE     ---       #696
      01000C17H   LINE      CODE     ---       #697
      01000C19H   LINE      CODE     ---       #698
      01000C1CH   LINE      CODE     ---       #699
      01000C1CH   LINE      CODE     ---       #700
      01000C1CH   LINE      CODE     ---       #701
      01000C1CH   LINE      CODE     ---       #702
      01000C1CH   LINE      CODE     ---       #703
      01000C1CH   LINE      CODE     ---       #704
      01000C1CH   LINE      CODE     ---       #705
      01000C1CH   LINE      CODE     ---       #706
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 59


      01000C1CH   LINE      CODE     ---       #707
      01000C1CH   LINE      CODE     ---       #708
      01000C1CH   LINE      CODE     ---       #709
      01000C24H   LINE      CODE     ---       #710
      01000C24H   LINE      CODE     ---       #711
      01000C26H   LINE      CODE     ---       #712
      01000C26H   LINE      CODE     ---       #713
      01000C2CH   LINE      CODE     ---       #715
      01000C2FH   LINE      CODE     ---       #716
      01000C2FH   LINE      CODE     ---       #717
      01000C33H   LINE      CODE     ---       #718
      01000C35H   LINE      CODE     ---       #720
      01000C35H   LINE      CODE     ---       #721
      01000C3CH   LINE      CODE     ---       #722
      01000C3CH   LINE      CODE     ---       #723
      01000C40H   LINE      CODE     ---       #724
      01000C42H   LINE      CODE     ---       #726
      01000C42H   LINE      CODE     ---       #727
      01000C47H   LINE      CODE     ---       #728
      01000C47H   LINE      CODE     ---       #729
      01000C47H   LINE      CODE     ---       #731
      01000C66H   LINE      CODE     ---       #732
      01000C66H   LINE      CODE     ---       #733
      01000C66H   LINE      CODE     ---       #734
      01000C66H   LINE      CODE     ---       #735
      01000C68H   LINE      CODE     ---       #736
      01000C6BH   LINE      CODE     ---       #737
      01000C6BH   LINE      CODE     ---       #738
      01000C6BH   LINE      CODE     ---       #739
      01000C6BH   LINE      CODE     ---       #740
      01000C6BH   LINE      CODE     ---       #741
      01000C6EH   LINE      CODE     ---       #742
      01000C6EH   LINE      CODE     ---       #743
      01000C6EH   LINE      CODE     ---       #744
      01000C6EH   LINE      CODE     ---       #745
      01000C6EH   LINE      CODE     ---       #746
      01000C71H   LINE      CODE     ---       #747
      01000C71H   LINE      CODE     ---       #748
      01000C71H   LINE      CODE     ---       #749
      01000C71H   LINE      CODE     ---       #750
      01000C71H   LINE      CODE     ---       #751
      01000C74H   LINE      CODE     ---       #752
      01000C74H   LINE      CODE     ---       #753
      01000C74H   LINE      CODE     ---       #754
      01000C74H   LINE      CODE     ---       #755
      01000C74H   LINE      CODE     ---       #756
      01000C74H   LINE      CODE     ---       #757
      01000C74H   LINE      CODE     ---       #758
      01000C74H   LINE      CODE     ---       #759
      01000C74H   LINE      CODE     ---       #760
      01000C74H   LINE      CODE     ---       #761
      01000C74H   LINE      CODE     ---       #762
      01000C7CH   LINE      CODE     ---       #763
      01000C7CH   LINE      CODE     ---       #764
      01000C7EH   LINE      CODE     ---       #765
      01000C7EH   LINE      CODE     ---       #766
      01000C84H   LINE      CODE     ---       #768
      01000C87H   LINE      CODE     ---       #769
      01000C87H   LINE      CODE     ---       #770
      01000C8BH   LINE      CODE     ---       #771
      01000C8DH   LINE      CODE     ---       #773
      01000C8DH   LINE      CODE     ---       #774
      01000C94H   LINE      CODE     ---       #775
      01000C94H   LINE      CODE     ---       #776
      01000C98H   LINE      CODE     ---       #777
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 60


      01000C9AH   LINE      CODE     ---       #779
      01000C9AH   LINE      CODE     ---       #780
      01000C9FH   LINE      CODE     ---       #781
      01000C9FH   LINE      CODE     ---       #782
      01000C9FH   LINE      CODE     ---       #784
      01000CBEH   LINE      CODE     ---       #785
      01000CBEH   LINE      CODE     ---       #786
      01000CBEH   LINE      CODE     ---       #787
      01000CBEH   LINE      CODE     ---       #788
      01000CBEH   LINE      CODE     ---       #789
      01000CC1H   LINE      CODE     ---       #790
      01000CC1H   LINE      CODE     ---       #791
      01000CC1H   LINE      CODE     ---       #792
      01000CC1H   LINE      CODE     ---       #793
      01000CC1H   LINE      CODE     ---       #794
      01000CC4H   LINE      CODE     ---       #795
      01000CC4H   LINE      CODE     ---       #796
      01000CC4H   LINE      CODE     ---       #797
      01000CC4H   LINE      CODE     ---       #798
      01000CC4H   LINE      CODE     ---       #799
      01000CC7H   LINE      CODE     ---       #800
      01000CC7H   LINE      CODE     ---       #801
      01000CC7H   LINE      CODE     ---       #802
      01000CC7H   LINE      CODE     ---       #803
      01000CC9H   LINE      CODE     ---       #804
      01000CCCH   LINE      CODE     ---       #805
      01000CCCH   LINE      CODE     ---       #806
      01000CCCH   LINE      CODE     ---       #807
      01000CCCH   LINE      CODE     ---       #808
      01000CCCH   LINE      CODE     ---       #809
      01000CCCH   LINE      CODE     ---       #810
      01000CCCH   LINE      CODE     ---       #811
      01000CCCH   LINE      CODE     ---       #812
      01000CCCH   LINE      CODE     ---       #813
      01000CCCH   LINE      CODE     ---       #814
      01000CCCH   LINE      CODE     ---       #815
      01000CD4H   LINE      CODE     ---       #816
      01000CD4H   LINE      CODE     ---       #817
      01000CD6H   LINE      CODE     ---       #818
      01000CD6H   LINE      CODE     ---       #819
      01000CDCH   LINE      CODE     ---       #821
      01000CE3H   LINE      CODE     ---       #822
      01000CE3H   LINE      CODE     ---       #823
      01000CE7H   LINE      CODE     ---       #824
      01000CE9H   LINE      CODE     ---       #826
      01000CE9H   LINE      CODE     ---       #827
      01000CEEH   LINE      CODE     ---       #828
      01000CEEH   LINE      CODE     ---       #830
      01000D07H   LINE      CODE     ---       #831
      01000D07H   LINE      CODE     ---       #832
      01000D07H   LINE      CODE     ---       #833
      01000D07H   LINE      CODE     ---       #834
      01000D09H   LINE      CODE     ---       #835
      01000D0BH   LINE      CODE     ---       #836
      01000D0BH   LINE      CODE     ---       #837
      01000D0BH   LINE      CODE     ---       #838
      01000D0BH   LINE      CODE     ---       #839
      01000D0BH   LINE      CODE     ---       #840
      01000D0DH   LINE      CODE     ---       #841
      01000D0DH   LINE      CODE     ---       #842
      01000D0DH   LINE      CODE     ---       #843
      01000D0DH   LINE      CODE     ---       #844
      01000D0DH   LINE      CODE     ---       #845
      01000D0FH   LINE      CODE     ---       #846
      01000D0FH   LINE      CODE     ---       #847
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 61


      01000D0FH   LINE      CODE     ---       #848
      01000D0FH   LINE      CODE     ---       #849
      01000D0FH   LINE      CODE     ---       #850
      01000D11H   LINE      CODE     ---       #851
      01000D11H   LINE      CODE     ---       #852
      01000D11H   LINE      CODE     ---       #853
      01000D11H   LINE      CODE     ---       #854
      01000D11H   LINE      CODE     ---       #855
      01000D11H   LINE      CODE     ---       #856
      01000D11H   LINE      CODE     ---       #857
      01000D11H   LINE      CODE     ---       #858
      01000D11H   LINE      CODE     ---       #859
      01000D11H   LINE      CODE     ---       #860
      01000D11H   LINE      CODE     ---       #861
      01000D19H   LINE      CODE     ---       #862
      01000D19H   LINE      CODE     ---       #863
      01000D1BH   LINE      CODE     ---       #864
      01000D1BH   LINE      CODE     ---       #865
      01000D21H   LINE      CODE     ---       #867
      01000D28H   LINE      CODE     ---       #868
      01000D28H   LINE      CODE     ---       #869
      01000D2CH   LINE      CODE     ---       #870
      01000D2EH   LINE      CODE     ---       #872
      01000D2EH   LINE      CODE     ---       #873
      01000D33H   LINE      CODE     ---       #874
      01000D33H   LINE      CODE     ---       #876
      01000D4CH   LINE      CODE     ---       #877
      01000D4CH   LINE      CODE     ---       #878
      01000D4CH   LINE      CODE     ---       #879
      01000D4CH   LINE      CODE     ---       #880
      01000D54H   LINE      CODE     ---       #881
      01000D56H   LINE      CODE     ---       #882
      01000D56H   LINE      CODE     ---       #883
      01000D56H   LINE      CODE     ---       #884
      01000D56H   LINE      CODE     ---       #885
      01000D5EH   LINE      CODE     ---       #886
      01000D60H   LINE      CODE     ---       #887
      01000D60H   LINE      CODE     ---       #888
      01000D60H   LINE      CODE     ---       #889
      01000D60H   LINE      CODE     ---       #890
      01000D64H   LINE      CODE     ---       #891
      01000D66H   LINE      CODE     ---       #892
      01000D66H   LINE      CODE     ---       #893
      01000D66H   LINE      CODE     ---       #894
      01000D66H   LINE      CODE     ---       #895
      01000D6EH   LINE      CODE     ---       #896
      01000D6EH   LINE      CODE     ---       #897
      01000D6EH   LINE      CODE     ---       #898
      01000D6EH   LINE      CODE     ---       #899
      01000D6EH   LINE      CODE     ---       #900
      01000D6EH   LINE      CODE     ---       #901
      01000D6EH   LINE      CODE     ---       #902
      01000D6EH   LINE      CODE     ---       #903
      01000D6EH   LINE      CODE     ---       #904
      01000D6EH   LINE      CODE     ---       #905
      01000D6EH   LINE      CODE     ---       #906
      01000D6EH   LINE      CODE     ---       #907
      01000D6EH   LINE      CODE     ---       #908
      01000D6EH   LINE      CODE     ---       #909
      01000D6EH   LINE      CODE     ---       #912
      01000D7BH   LINE      CODE     ---       #913
      01000D7BH   LINE      CODE     ---       #914
      01000D7FH   LINE      CODE     ---       #915
      01000D83H   LINE      CODE     ---       #916
      01000D86H   LINE      CODE     ---       #917
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 62


      01000D89H   LINE      CODE     ---       #918
      01000D8BH   LINE      CODE     ---       #919
      01000D8BH   LINE      CODE     ---       #922
      01000D91H   LINE      CODE     ---       #923
      01000D91H   LINE      CODE     ---       #924
      01000D94H   LINE      CODE     ---       #925
      01000D96H   LINE      CODE     ---       #927
      01000DB0H   LINE      CODE     ---       #928
      01000DB0H   LINE      CODE     ---       #929
      01000DB6H   LINE      CODE     ---       #930
      01000DBFH   LINE      CODE     ---       #931
      01000DBFH   LINE      CODE     ---       #932
      01000DC1H   LINE      CODE     ---       #933
      01000DC1H   LINE      CODE     ---       #934
      01000DC3H   LINE      CODE     ---       #936
      01000DC3H   LINE      CODE     ---       #937
      01000DCAH   LINE      CODE     ---       #938
      01000DCAH   LINE      CODE     ---       #940
      01000DE5H   LINE      CODE     ---       #941
      01000DE5H   LINE      CODE     ---       #942
      01000DEBH   LINE      CODE     ---       #943
      01000DF7H   LINE      CODE     ---       #944
      01000DF7H   LINE      CODE     ---       #945
      01000DF9H   LINE      CODE     ---       #946
      01000DF9H   LINE      CODE     ---       #947
      01000DFCH   LINE      CODE     ---       #949
      01000DFCH   LINE      CODE     ---       #950
      01000E03H   LINE      CODE     ---       #951
      01000E03H   LINE      CODE     ---       #952
      01000E03H   LINE      CODE     ---       #953
      01000E03H   LINE      CODE     ---       #954
      ---         BLOCKEND  ---      ---       LVL=0

      01001531H   BLOCK     CODE     VOID      LVL=0
      ---         BLOCKEND  ---      ---       LVL=0

      01001535H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      01001535H   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      01001535H   LINE      CODE     ---       #957
      01001535H   LINE      CODE     ---       #958
      01001535H   LINE      CODE     ---       #960
      0100153FH   LINE      CODE     ---       #961
      01001554H   LINE      CODE     ---       #962
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      00000004H   SYMBOL    DATA     WORD      x

      01001033H   BLOCK     CODE     ---       LVL=0
      01001033H   BLOCK     CODE     NEAR LAB  LVL=1
      00000004H   SYMBOL    DATA     FLOAT     adc_result
      02000025H   SYMBOL    XDATA    ---       result
      02000031H   SYMBOL    XDATA    WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      01001033H   LINE      CODE     ---       #1006
      01001033H   LINE      CODE     ---       #1007
      01001033H   LINE      CODE     ---       #1012
      0100103AH   LINE      CODE     ---       #1013
      0100103AH   LINE      CODE     ---       #1014
      0100103DH   LINE      CODE     ---       #1015
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 63


      01001042H   LINE      CODE     ---       #1016
      01001065H   LINE      CODE     ---       #1017
      01001082H   LINE      CODE     ---       #1018
      010010BCH   LINE      CODE     ---       #1020
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEMO_GPIO
      01000F68H   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 64


      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 65


      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000F68H   BLOCK     CODE     ---       LVL=0
      01000F68H   LINE      CODE     ---       #68
      01000F68H   LINE      CODE     ---       #69
      01000F68H   LINE      CODE     ---       #70
      01000F7DH   LINE      CODE     ---       #73
      01000F81H   LINE      CODE     ---       #74
      01000F85H   LINE      CODE     ---       #75
      01000F89H   LINE      CODE     ---       #76
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 66


      01000F8DH   LINE      CODE     ---       #77
      01000F90H   LINE      CODE     ---       #78
      01000F93H   LINE      CODE     ---       #79
      01000F96H   LINE      CODE     ---       #80
      01000F99H   LINE      CODE     ---       #83
      01000F9DH   LINE      CODE     ---       #84
      01000F9FH   LINE      CODE     ---       #85
      01000FA2H   LINE      CODE     ---       #86
      01000FA5H   LINE      CODE     ---       #87
      01000FA9H   LINE      CODE     ---       #90
      01000FADH   LINE      CODE     ---       #91
      01000FB0H   LINE      CODE     ---       #92
      01000FB7H   LINE      CODE     ---       #94
      01000FBCH   LINE      CODE     ---       #95
      01000FBFH   LINE      CODE     ---       #96
      01000FC6H   LINE      CODE     ---       #98
      01000FCBH   LINE      CODE     ---       #99
      01000FCEH   LINE      CODE     ---       #100
      01000FD5H   LINE      CODE     ---       #103
      01000FDBH   LINE      CODE     ---       #104
      01000FE2H   LINE      CODE     ---       #105
      01000FE5H   LINE      CODE     ---       #109
      01000FEAH   LINE      CODE     ---       #110
      01000FEDH   LINE      CODE     ---       #111
      01000FF1H   LINE      CODE     ---       #112
      01000FF4H   LINE      CODE     ---       #113
      01000FF6H   LINE      CODE     ---       #116
      01000FFAH   LINE      CODE     ---       #117
      01000FFEH   LINE      CODE     ---       #118
      01001001H   LINE      CODE     ---       #119
      01001004H   LINE      CODE     ---       #120
      0100100BH   LINE      CODE     ---       #121
      0100100FH   LINE      CODE     ---       #124
      01001015H   LINE      CODE     ---       #125
      0100101CH   LINE      CODE     ---       #126
      0100101FH   LINE      CODE     ---       #128
      01001025H   LINE      CODE     ---       #129
      0100102CH   LINE      CODE     ---       #130
      0100102FH   LINE      CODE     ---       #133
      01001032H   LINE      CODE     ---       #135
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEMO_UART
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 67


      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 68


      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 69


      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000006H   SYMBOL    DATA     WORD      BRTValue
      00000007H   SYMBOL    DATA     CHAR      ch

      ---         MODULE    ---      ---       DEMO_TIMER
      010014BCH   PUBLIC    CODE     ---       TMR4_Config
      01001493H   PUBLIC    CODE     ---       TMR1_Config
      01001469H   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 70


      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 71


      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 72


      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001469H   BLOCK     CODE     ---       LVL=0
      01001469H   LINE      CODE     ---       #3
      01001469H   LINE      CODE     ---       #4
      01001469H   LINE      CODE     ---       #9
      01001471H   LINE      CODE     ---       #16
      01001477H   LINE      CODE     ---       #23
      01001480H   LINE      CODE     ---       #29
      01001485H   LINE      CODE     ---       #36
      0100148BH   LINE      CODE     ---       #37
      0100148EH   LINE      CODE     ---       #43
      ---         BLOCKEND  ---      ---       LVL=0

      01001493H   BLOCK     CODE     ---       LVL=0
      01001493H   LINE      CODE     ---       #46
      01001493H   LINE      CODE     ---       #47
      01001493H   LINE      CODE     ---       #52
      0100149CH   LINE      CODE     ---       #57
      010014A3H   LINE      CODE     ---       #62
      010014ACH   LINE      CODE     ---       #67
      010014B1H   LINE      CODE     ---       #72
      010014B4H   LINE      CODE     ---       #73
      010014B7H   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      010014BCH   BLOCK     CODE     ---       LVL=0
      010014BCH   LINE      CODE     ---       #81
      010014BCH   LINE      CODE     ---       #82
      010014BCH   LINE      CODE     ---       #87
      010014C5H   LINE      CODE     ---       #94
      010014CCH   LINE      CODE     ---       #101
      010014D5H   LINE      CODE     ---       #107
      010014DAH   LINE      CODE     ---       #114
      010014DDH   LINE      CODE     ---       #115
      010014E0H   LINE      CODE     ---       #121
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEMO_ADC
      01001624H   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 73


      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 74


      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 75


      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001624H   BLOCK     CODE     ---       LVL=0
      01001624H   LINE      CODE     ---       #67
      01001624H   LINE      CODE     ---       #68
      01001624H   LINE      CODE     ---       #70
      0100162BH   LINE      CODE     ---       #72
      01001630H   LINE      CODE     ---       #73
      01001635H   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EEPROM
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 76


      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 77


      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 78


      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       ?C?FPADD
      010000BDH   PUBLIC    CODE     ---       ?C?FPADD
      010000B9H   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FPDIV
      010001B1H   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FPCMP
      01000250H   PUBLIC    CODE     ---       ?C?FPCMP
      0100024EH   PUBLIC    CODE     ---       ?C?FPCMP3

      ---         MODULE    ---      ---       ?C?FCAST
      010002D1H   PUBLIC    CODE     ---       ?C?FCASTC
      010002CCH   PUBLIC    CODE     ---       ?C?FCASTI
      010002C7H   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       ?C?CASTF
      01000305H   PUBLIC    CODE     ---       ?C?CASTF

      ---         MODULE    ---      ---       PRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      0100033CH   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000371H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      0100037BH   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000353H   PUBLIC    CODE     ---       ?C?FPRESULT
      01000367H   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000378H   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000386H   PUBLIC    CODE     ---       ?C?FPROUND

LX51 LINKER/LOCATER V4.66.97.0                                                        07/21/2025  15:36:55  PAGE 79


      ---         MODULE    ---      ---       ?C?FPCONVERT
      010003C3H   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPMUL
      010004C8H   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FTNPWR
      010005FEH   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      0100112AH   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?CLDPTR
      0100063EH   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000657H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000684H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?IMUL
      01000696H   PUBLIC    CODE     ---       ?C?IMUL

      ---         MODULE    ---      ---       ?C?ILDIX
      010006A8H   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?LMUL
      010006BEH   PUBLIC    CODE     ---       ?C?LMUL

      ---         MODULE    ---      ---       ?C?ULDIV
      01000749H   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      010007DBH   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      010007E9H   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      010007F5H   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      01000826H   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      0100083DH   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
      01000846H   PUBLIC    CODE     ---       ?C?CCASE

      ---         MODULE    ---      ---       ABS

Program Size: data=10.7 xdata=51 const=0 code=5653
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
