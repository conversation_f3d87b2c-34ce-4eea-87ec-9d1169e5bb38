C51 COMPILER V9.60.0.0   DEMO_GPIO                                                         07/21/2025 16:42:41 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE DEMO_GPIO
OBJECT MODULE PLACED IN .\Objects\demo_gpio.obj
COMPILER INVOKED BY: D:\KeilC51\C51\BIN\C51.EXE ..\code\demo_gpio.c LARGE OMF2 OPTIMIZE(8,SPEED) BROWSE INCDIR(..\code;.
                    -.\Libary\Device\CMS8S6990\Include;..\Libary\StdDriver\inc) DEFINE(USE_FORMULA) DEBUG PRINT(.\Listings\demo_gpio.lst) TAB
                    -S(2) OBJECT(.\Objects\demo_gpio.obj)

line level    source

   1          /*******************************************************************************
   2          * Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
   3          *
   4          * This software is owned and published by:
   5          * CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
   6          *
   7          * BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
   8          * BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
   9          *
  10          * This software contains source code for use with CMS
  11          * components. This software is licensed by CMS to be adapted only
  12          * for use in systems utilizing CMS components. CMS shall not be
  13          * responsible for misuse or illegal use of this software for devices not
  14          * supported herein. CMS is providing this software "AS IS" and will
  15          * not be responsible for issues arising from incorrect user implementation
  16          * of the software.
  17          *
  18          * This software may be replicated in part or whole for the licensed use,
  19          * with the restriction that this Disclaimer and Copyright notice must be
  20          * included with each copy of this software, whether used in part or whole,
  21          * at all times.
  22          */
  23          
  24          /****************************************************************************/
  25          /** \file demo_gpio.c
  26          **
  27          **  
  28          **
  29          **  History:
  30          **  
  31          *****************************************************************************/
  32          /****************************************************************************/
  33          /*  include files
  34          *****************************************************************************/
  35          #include "demo_gpio.h"
  36          
  37          /****************************************************************************/
  38          /*  Local pre-processor symbols('#define')
  39          ****************************************************************************/
  40          
  41          /****************************************************************************/
  42          /*  Global variable definitions(declared in header file with 'extern')
  43          ****************************************************************************/
  44          
  45          /****************************************************************************/
  46          /*  Local type definitions('typedef')
  47          ****************************************************************************/
  48          
  49          /****************************************************************************/
  50          /*  Local variable  definitions('static')
  51          ****************************************************************************/
  52          
  53          /****************************************************************************/
C51 COMPILER V9.60.0.0   DEMO_GPIO                                                         07/21/2025 16:42:41 PAGE 2   

  54          /*  Local function prototypes('static')
  55          ****************************************************************************/
  56          
  57          /****************************************************************************/
  58          /*  Function implementation - global ('extern') and local('static')
  59          ****************************************************************************/
  60          extern bit uart;
  61          /******************************************************************************
  62           ** \brief   GPIO_Config
  63           ** \param [in] none
  64           **          GPIO interrupt config
  65           ** \return  none
  66           ** \note  
  67           ******************************************************************************/
  68          void GPIO_Config(void)
  69          {
  70   1        P0TRIS=0xff;P0=0x0;P1TRIS=0xff;P1=0x0;P2TRIS=0xff;P2=0x0;P3TRIS=0xff;P3=0x0;
  71   1        
  72   1        //A1-P14 B1-P13 A2-P04 B2-P05 (motor control pins)
  73   1        GPIO_SET_MUX_MODE(P14CFG,GPIO_MUX_GPIO);
  74   1        GPIO_SET_MUX_MODE(P13CFG,GPIO_MUX_GPIO);
  75   1        GPIO_SET_MUX_MODE(P04CFG,GPIO_MUX_GPIO);
  76   1        GPIO_SET_MUX_MODE(P05CFG,GPIO_MUX_GPIO);
  77   1        GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_4);  // P14
  78   1        GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_3);  // P13
  79   1        GPIO_ENABLE_OUTPUT(P0TRIS,GPIO_PIN_4);  // P04
  80   1        GPIO_ENABLE_OUTPUT(P0TRIS,GPIO_PIN_5);  // P05
  81   1        
  82   1        //P22-LEDG P23-LEDR
  83   1        GPIO_SET_MUX_MODE(P22CFG, GPIO_MUX_GPIO);   
  84   1        GPIO_SET_MUX_MODE(P23CFG, GPIO_MUX_GPIO);   
  85   1        GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_2);     
  86   1        GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_3);   
  87   1        P22=0;P23=0;
  88   1        
  89   1        //K1-P17 K2-P16 K3-P15 (key inputs)
  90   1        GPIO_SET_MUX_MODE(P17CFG, GPIO_MUX_GPIO);   // set as GPIO mode
  91   1        GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_7);      // set as input mode
  92   1        GPIO_ENABLE_UP(P1UP, GPIO_PIN_7);       // enable pull-up
  93   1        
  94   1        GPIO_SET_MUX_MODE(P16CFG, GPIO_MUX_GPIO);   // set as GPIO mode
  95   1        GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_6);      // set as input mode
  96   1        GPIO_ENABLE_UP(P1UP, GPIO_PIN_6); 
  97   1        
  98   1        GPIO_SET_MUX_MODE(P15CFG, GPIO_MUX_GPIO);   // set as GPIO mode
  99   1        GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_5);      // set as input mode
 100   1        GPIO_ENABLE_UP(P1UP, GPIO_PIN_5);
 101   1        
 102   1        //K2 falling edge interrupt
 103   1        GPIO_SET_INT_MODE(P16EICFG, GPIO_INT_FALLING);  // set as falling edge interrupt mode
 104   1        GPIO_EnableInt(GPIO1, GPIO_PIN_6_MSK);      // enable P16 interrupt
 105   1        IRQ_SET_PRIORITY(IRQ_P1, IRQ_PRIORITY_LOW);
 106   1      
 107   1        
 108   1        //CHG_CHARGE-P24  CHG_FULL-P25
 109   1        GPIO_SET_MUX_MODE(P24CFG, GPIO_MUX_GPIO); 
 110   1        GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_4);
 111   1        GPIO_ENABLE_UP(P2UP, GPIO_PIN_4);
 112   1        
 113   1        GPIO_SET_MUX_MODE(P25CFG, GPIO_MUX_GPIO);
 114   1        GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_5);
 115   1        GPIO_ENABLE_UP(P2UP, GPIO_PIN_5);
C51 COMPILER V9.60.0.0   DEMO_GPIO                                                         07/21/2025 16:42:41 PAGE 3   

 116   1        
 117   1        //CHG_CHARGE P24 falling edge interrupt
 118   1        GPIO_SET_INT_MODE(P24EICFG, GPIO_INT_FALLING);  // set as falling edge interrupt mode
 119   1        GPIO_EnableInt(GPIO2, GPIO_PIN_4_MSK);      // enable interrupt
 120   1        IRQ_SET_PRIORITY(IRQ_P2, IRQ_PRIORITY_LOW);
 121   1        
 122   1        //CHG_FULL P25 falling edge interrupt
 123   1        GPIO_SET_INT_MODE(P25EICFG, GPIO_INT_FALLING);  // set as falling edge interrupt mode
 124   1        GPIO_EnableInt(GPIO2, GPIO_PIN_5_MSK);      // enable interrupt
 125   1        IRQ_SET_PRIORITY(IRQ_P2, IRQ_PRIORITY_LOW);
 126   1        
 127   1        
 128   1        IRQ_ALL_ENABLE();
 129   1      
 130   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    187    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
   EDATA SIZE       =   ----    ----
   HDATA SIZE       =   ----    ----
   XDATA CONST SIZE =   ----    ----
   FAR CONST SIZE   =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
