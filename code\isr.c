/*******************************************************************************
* Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
*
* This software is owned and published by:
* CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
*
* BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
* BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
*
* This software contains source code for use with CMS
* components. This software is licensed by CMS to be adapted only
* for use in systems utilizing CMS components. CMS shall not be
* responsible for misuse or illegal use of this software for devices not
* supported herein. CMS is providing this software "AS IS" and will
* not be responsible for issues arising from incorrect user implementation
* of the software.
*
* This software may be replicated in part or whole for the licensed use,
* with the restriction that this Disclaimer and Copyright notice must be
* included with each copy of this software, whether used in part or whole,
* at all times.
*/

/****************************************************************************/
/** \file isr.c
**
** 
**
**	History:
**		
*****************************************************************************/
/****************************************************************************/
/*	include files
*****************************************************************************/
#include "cms8s6990.h"
#include "define.h"
#include "eeprom.h"

#define stop_RF_cnts		2

extern int16_t tmr1_cnt;

//遥控
// extern uint32_t rf_dat;
// extern bit rev_success;
extern uint16_t result;
// bit stop_RF;
// uint8_t stop_RF_cnt;
// uint16_t ll_w,hh_w,lst_ll_w,lst_hh_w;
// uint8_t tb_ok=0,index=0,old_rc5=1;    //RF数据线低电平高电平时间计数
// uint8_t da,index,da_h,da_l;
// bit can_da=0;   //头码过后,可以接收数据标志位
// bit da_valid=0;   //收到有效da数据标志位
//按键
extern uint8_t K1_cnt,K2_cnt,K3_cnt;
extern bit K1_cnt_EN,K2_cnt_EN,K3_cnt_EN;
extern volatile bit longhit;
extern uint16_t longhit_cnt;
uint8_t K1_release_cnt,K2_release_cnt,K3_release_cnt;
//马达
int16_t  tmr4_cnt;          //递增用(TMR4中断内用)
volatile int16_t  tmr4_cnt1=5;     //递增目标(遥控修改)
uint16_t  interval_cnt;
extern volatile bit speedup;
uint16_t speedup_cnt;

// extern bit no_RF_signal;
// extern uint8_t no_RF_signal_cnt;


extern uint8_t motor_status;
extern bit can_RF_change_status;
uint8_t can_RF_change_status_cnt;


extern bit canADC;
uint8_t canADC_cnt;
extern bit ledonoff;
uint8_t ledonoff_cnt;
extern bit ledonoff1;
uint8_t ledonoff1_cnt;

extern uint8_t dly;

/******************************************************************************
 ** \brief	 INT0 interrupt service function
 **			
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void INT0_IRQHandler(void)  interrupt INT0_VECTOR
{

}
/******************************************************************************
 ** \brief	 Timer 0 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer0_IRQHandler(void)  interrupt TMR0_VECTOR 
{
	TH0 = 256-100;
	TL0 = 256-100;
	
	// if(stop_RF==0)
	// {			
	// 	if(RF_DATA==1)
	// 	{
	// 		hh_w++;
	// 		if(ll_w!=0)	lst_ll_w=ll_w;   //记录上次低电平时间；
	// 		ll_w=0;    //低电平时间清空
	// 		//判断有无收到头码
	// 		if(tb_ok==0)
	// 		{				
	// 			//if((lst_hh_w>=8)&&(lst_hh_w<=12)&&(lst_ll_w>=280)&&(lst_ll_w<=340))
	// 			if((lst_hh_w>=6)&&(lst_hh_w<=11)&&(lst_ll_w>=192)&&(lst_ll_w<=320))
	// 			{
	// 				tb_ok=1;
	// 			}				
	// 		}
			
	// 		if((tb_ok==1)&&(can_da==1)&&(da_valid==0))   //已经收到头码
	// 		{
	// 			//if((lst_hh_w>=8)&&(lst_hh_w<=12)&&(lst_ll_w>=27)&&(lst_ll_w<=33))  //收到数据0
	// 			if((lst_hh_w>=5)&&(lst_hh_w<=10)&&(lst_ll_w>=17)&&(lst_ll_w<=25))  //收到数据0
	// 			{
	// 				da=0;
	// 				da_l++;
	// 			}
	// 			//else if((lst_ll_w>=8)&&(lst_ll_w<=12)&&(lst_hh_w>=27)&&(lst_hh_w<=33))  //收到数据1
	// 			else if((lst_ll_w>=5)&&(lst_ll_w<=10)&&(lst_hh_w>=17)&&(lst_hh_w<=25))  //收到数据1
	// 			{
	// 				da=1;
	// 				da_h++;
	// 			}
	// 			else
	// 			{
	// 				da=2;
	// 				tb_ok=0;    //中途接收出错，重新开始
	// 				index=0;
	// 				rf_dat=0;
	// 				can_da=0;
	// 			}
				
	// 			//组合数据
	// 			if(da!=2)
	// 			{
	// 				da_valid=1;   //已收到有效数据(此位在RF_DATA==0时清零)				
	// 				if(da==1)
	// 				{
	// 					rf_dat|=1;
	// 				}					
	// 				rf_dat=rf_dat<<1;
	// 				index++;
	// 				if(index==24)      //接收到index个数据
	// 				{		
	// 					stop_RF=1;   //接收成功,停止接收
	// 					stop_RF_cnt=stop_RF_cnts;
	// 					rf_dat=rf_dat>>1;
	// 					rev_success=1;
	// 					index=0;
	// 					tb_ok=0;
	// 					can_da=0;
	// 					da_valid=0;
	// 				}
	// 			}
	// 		}
			
	// 	}
	// 	if(RF_DATA==0)
	// 	{
	// 		if((tb_ok==1)&&(can_da==0))
	// 		{
	// 			can_da=1;   //可以开始接收数据
	// 		}
	// 		if(da_valid==1) da_valid=0;
	// 		ll_w++;
	// 		if(hh_w!=0) lst_hh_w=hh_w;
	// 		hh_w=0;
	// 	}
	// }	
}
/******************************************************************************
 ** \brief	 INT0 interrupt service function
 **			
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void INT1_IRQHandler(void)  interrupt INT1_VECTOR
{
	;
}
/******************************************************************************
 ** \brief	 Timer 1 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer1_IRQHandler(void)  interrupt TMR1_VECTOR 
{
	TH1 = (65536-20000)>>8;
	TL1 = 65536-20000;
  
  if(motor_status!=0)
  {
    ledonoff1_cnt++;
    if(ledonoff1_cnt>5)
    {      
      ledonoff1^=1;
      ledonoff1_cnt=0;
    }      
  }
	
	if(result!=0)
	{
		longhit_cnt++;
		if(longhit_cnt>500)
		{
			longhit=1;
		}
	}
	else
	{
		longhit=0;longhit_cnt=0;
	}
	//马达换向加速
	if(speedup==1)
	{
		speedup_cnt++;
		if(speedup_cnt>dly)
		{
			speedup=0;
			speedup_cnt=0;
		}
	}
  else
  {
    speedup_cnt=0;
  }
	
	//遥控接收停止倒计时
	// if(stop_RF_cnt>0)   
	// {
	// 	stop_RF_cnt--;
	// 	if(stop_RF_cnt==0)
	// 	{
	// 		stop_RF=0;
	// 	}		
	// }
	
	//没有遥控信号倒计时
//   if(no_RF_signal==0)
//   {
//     no_RF_signal_cnt++;
//     if(no_RF_signal_cnt>20)
//     {
//       no_RF_signal=1;
//     }
//   }
 
  
  
//   if(can_RF_change_status==0)
//   {
//     can_RF_change_status_cnt++;
//     if(can_RF_change_status_cnt>50)
//     {
//       can_RF_change_status=1;
//       can_RF_change_status_cnt=0;
//     }
//   }

	
	//按键
	if(K1_cnt_EN)
	{
		if(K1==0) K1_cnt++;
	}
	else
	{
		K1_cnt=0;
	}
	
	if(K2_cnt_EN)
	{
		if(K2==0) K2_cnt++;
	}
	else
	{
		K2_cnt=0;
	}
	
	if(K3_cnt_EN)
	{
		if(K3==0) K3_cnt++;
	}
	else
	{
		K3_cnt=0;
	}
  
  //按键释放检测
  if(K1_cnt_EN==0)
  {
    if(K1==1)
    {
      K1_release_cnt++;
      if(K1_release_cnt>10) 
      {
        K1_cnt_EN=1;
        K1_release_cnt=0;
      }
    }
    else
    {
      K1_release_cnt=0;
    }
  }
  
  if(K2_cnt_EN==0)
  {
    if(K2==1)
    {
      K2_release_cnt++;
      if(K2_release_cnt>10) 
      {
        K2_cnt_EN=1;
        K2_release_cnt=0;
      }
    }
    else
    {
      K2_release_cnt=0;
    }
  }
  
  if(K3_cnt_EN==0)
  {
    if(K3==1)
    {
      K3_release_cnt++;
      if(K3_release_cnt>10) 
      {
        K3_cnt_EN=1;
        K3_release_cnt=0;
      }
    }
    else
    {
      K3_release_cnt=0;
    }
  }
	
	//定时ADC检测
	canADC_cnt++;
	if(canADC_cnt>200)	// 每200次定时器1中断(10ms*200=2s)触发一次电压检测
	{
		canADC_cnt=0;	
		canADC=1;
	}
	
	//LED闪烁控制
	ledonoff_cnt++;
	if(ledonoff_cnt>50)
	{
		ledonoff_cnt=0;
		ledonoff=~ledonoff;
	}
}
/******************************************************************************
 ** \brief	 UART 0 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void UART0_IRQHandler(void)  interrupt UART0_VECTOR 
{

}
/******************************************************************************
 ** \brief	 Timer 2 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer2_IRQHandler(void)  interrupt TMR2_VECTOR 
{
	
}
/******************************************************************************
 ** \brief	 UART 1 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void UART1_IRQHandler(void)  interrupt UART1_VECTOR 
{
	;
}
/******************************************************************************
 ** \brief	 GPIO 0 interrupt service function
 **	
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void P0EI_IRQHandler(void)  interrupt P0EI_VECTOR 
{
	if(GPIO_GetIntFlag(GPIO0,GPIO_PIN_4))
	{
		GPIO_ClearIntFlag(GPIO0,GPIO_PIN_4);
	}
}
/******************************************************************************
 ** \brief	 GPIO 1 interrupt service function
 **
 ** \param [in]  none
 **
 ** \return none
 ******************************************************************************/
void P1EI_IRQHandler(void)  interrupt P1EI_VECTOR
{
	// 处理K2按键中断 (P16)
	if(GPIO_GetIntFlag(GPIO1, GPIO_PIN_6))
	{
		GPIO_ClearIntFlag(GPIO1, GPIO_PIN_6);
	}
}
/******************************************************************************
 ** \brief	 GPIO 2 interrupt service function
 **
 ** \param [in]  none
 **
 ** \return none
 ******************************************************************************/
void P2EI_IRQHandler(void)  interrupt P2EI_VECTOR
{
	// 处理充电状态中断
	if(GPIO_GetIntFlag(GPIO2, GPIO_PIN_4))  // CHG_CHARGE-P24
	{
		GPIO_ClearIntFlag(GPIO2, GPIO_PIN_4);
	}

	if(GPIO_GetIntFlag(GPIO2, GPIO_PIN_5))  // CHG_FULL-P25
	{
		GPIO_ClearIntFlag(GPIO2, GPIO_PIN_5);
	}
}
/******************************************************************************
 ** \brief	 GPIO 3 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void P3EI_IRQHandler(void)  interrupt P3EI_VECTOR 
{
	if(GPIO_GetIntFlag(GPIO3,GPIO_PIN_2))
	{
		GPIO_ClearIntFlag(GPIO3,GPIO_PIN_2);
	}
	if(GPIO_GetIntFlag(GPIO3,GPIO_PIN_6))
	{
		GPIO_ClearIntFlag(GPIO3,GPIO_PIN_6);
	}
}

/******************************************************************************
 ** \brief	 LVD interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void LVD_IRQHandler(void)  interrupt LVD_VECTOR 
{

}
/******************************************************************************
 ** \brief	 LSE interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void LSE_IRQHandler(void)  interrupt LSE_VECTOR 
{

}

/********************************************************************************
 ** \brief	 ACMP interrupt service function
 **			
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void ACMP_IRQHandler(void)  interrupt ACMP_VECTOR 
{
	
}
/******************************************************************************
 ** \brief	 Timer 3 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer3_IRQHandler(void)  interrupt TMR3_VECTOR 
{

}
/******************************************************************************
 ** \brief	 Timer 4 interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
******************************************************************************/
void Timer4_IRQHandler(void)  interrupt TMR4_VECTOR 
{
	TH4 = (65536-2000)>>8;
	TL4 = 65536-2000;
	
	if(motor_status!=0)
	{
		tmr4_cnt++;
		if(tmr4_cnt>tmr4_cnt1)
		{
			tmr4_cnt=0;
			interval_cnt++;
			if(interval_cnt>3) interval_cnt=0;		
		}
	}
	else
	{
		tmr4_cnt=0;
		interval_cnt=0;
	}
}
/******************************************************************************
 ** \brief	 EPWM interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void EPWM_IRQHandler(void)  interrupt EPWM_VECTOR
{

}
/******************************************************************************
 ** \brief	 ADC interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void ADC_IRQHandler(void)  interrupt ADC_VECTOR 
{

}
/******************************************************************************
 ** \brief	 WDT interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void WDT_IRQHandler(void)  interrupt WDT_VECTOR 
{

}
/******************************************************************************
 ** \brief	I2C interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void I2C_IRQHandler(void)  interrupt I2C_VECTOR 
{
	;
}
/******************************************************************************
 ** \brief	SPI interrupt service function
 **
 ** \param [in]  none   
 **
 ** \return none
 ******************************************************************************/
void SPI_IRQHandler(void)  interrupt SPI_VECTOR 
{
	;
}















